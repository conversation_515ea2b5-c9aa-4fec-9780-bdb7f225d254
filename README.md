# CodeSprint - Full Stack Learning Platform

A comprehensive coding education platform with separate backend and frontend architecture.

## 🏗️ Project Structure

```
code_sprint/
├── backend/                 # Express.js API Server
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/         # MongoDB models
│   │   ├── routes/         # API routes
│   │   ├── utils/          # Utilities (DB connection, etc.)
│   ├── .env               # Backend environment variables
│   ├── package.json       # Backend dependencies
│   └── server.js          # Express server entry point
├── frontend/               # Next.js Frontend
│   ├── src/
│   │   ├── pages/         # Next.js pages
│   │   ├── styles/        # CSS styles
│   │   ├── utils/         # Frontend utilities
│   │   └── data/          # Static data
│   ├── public/            # Static assets
│   ├── .env.local         # Frontend environment variables
│   ├── package.json       # Frontend dependencies
│   └── next.config.mjs    # Next.js configuration
└── README.md              # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- MongoDB Atlas account (or local MongoDB)

### 1. Install Dependencies

```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 2. Environment Setup

**Backend (.env file is already created in backend/)**
```env
MONGODB_URI=mongodb+srv://admin:<EMAIL>/course?retryWrites=true&w=majority&appName=Cluster0
JWT_SECRET=5d750438a20a314afb1d6fdaaa3e033b1fd5ece9113391f5ab1f45ce21fd25db
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
```

**Frontend (.env.local file is already created in frontend/)**
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

### 3. Run the Application

**Option 1: Run Both Servers Separately**

Terminal 1 (Backend):
```bash
cd backend
npm run dev
```

Terminal 2 (Frontend):
```bash
cd frontend
npm run dev
```

**Option 2: Run Both Servers Concurrently (Recommended)**

From the root directory:
```bash
npm install -g concurrently
concurrently "cd backend && npm run dev" "cd frontend && npm run dev"
```

### 4. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **API Health Check**: http://localhost:5000/api/health

## 🧪 Testing the Setup

### 1. Test Backend API
```bash
# Test health endpoint
curl http://localhost:5000/api/health

# Expected response:
# {"status":"OK","message":"CodeSprint Backend API is running","timestamp":"..."}
```

### 2. Test Frontend
- Open http://localhost:3000
- You should see the CodeSprint homepage
- Try navigating to login page: http://localhost:3000/login

### 3. Test Full Stack Integration
1. Go to http://localhost:3000/register
2. Create a new account
3. Login with the created account
4. Check if you're redirected to the dashboard

## 📁 Key Changes Made

### Backend Changes:
- ✅ Converted Next.js API routes to Express.js routes
- ✅ Set up proper Express server with CORS
- ✅ Moved models and utilities to backend
- ✅ Added proper error handling and logging
- ✅ Configured environment variables

### Frontend Changes:
- ✅ Restructured to use `src/` directory
- ✅ Created API utility for backend communication
- ✅ Updated import paths
- ✅ Configured Next.js proxy for API calls
- ✅ Updated authentication flow

## 🔧 Development Scripts

### Backend Scripts
```bash
cd backend
npm run dev      # Start development server with nodemon
npm start        # Start production server
```

### Frontend Scripts
```bash
cd frontend
npm run dev      # Start Next.js development server
npm run build    # Build for production
npm start        # Start production server
npm run lint     # Run ESLint
```

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/verify-token` - Verify JWT token

### Payment
- `POST /api/payment/process` - Process payment
- `POST /api/payment/callback` - Payment callback

## 🔍 Troubleshooting

### Common Issues:

1. **CORS Errors**: Make sure backend is running on port 5000
2. **Database Connection**: Verify MongoDB URI in backend/.env
3. **API Not Found**: Check if backend server is running
4. **Frontend Build Errors**: Ensure all dependencies are installed

### Debug Steps:
1. Check if both servers are running
2. Verify environment variables
3. Check browser console for errors
4. Check backend logs for API errors

## 📦 Dependencies

### Backend
- Express.js - Web framework
- Mongoose - MongoDB ODM
- JWT - Authentication
- bcryptjs - Password hashing
- CORS - Cross-origin requests

### Frontend
- Next.js - React framework
- Axios - HTTP client
- Tailwind CSS - Styling
- React Hook Form - Form handling

## 🚀 Deployment

### Backend Deployment
- Deploy to services like Heroku, Railway, or DigitalOcean
- Set environment variables in production
- Use MongoDB Atlas for database

### Frontend Deployment
- Deploy to Vercel, Netlify, or similar
- Update `NEXT_PUBLIC_API_URL` to production backend URL

## 📝 Notes

- The original monolithic structure has been separated into backend and frontend
- All API calls now go through the backend server
- Authentication is handled via JWT tokens
- Database models and utilities are now in the backend only
