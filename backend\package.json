{"name": "codesprint-backend", "version": "1.0.0", "description": "Backend API for CodeSprint application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "mongoose": "^8.5.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "api", "backend", "codesprint"], "author": "CodeSprint Team", "license": "ISC"}