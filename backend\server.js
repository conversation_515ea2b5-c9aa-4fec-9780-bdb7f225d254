const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const dbConnect = require('./src/utils/dbConnect');

// Import routes
const authRoutes = require('./src/routes/auth');
const paymentRoutes = require('./src/routes/payment');

const app = express();
const PORT = process.env.PORT || 5000;

// Connect to database
dbConnect();

// Middleware
app.use(helmet());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/payment', paymentRoutes);

// Legacy routes for backward compatibility (temporary fix)
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('./src/models/User');

app.post('/api/login', async (req, res) => {
  await dbConnect();

  try {
    const { username, password } = req.body;

    // Check if the user exists (can login with username or email)
    const user = await User.findOne({
      $or: [{ username }, { email: username }]
    });

    if (!user) {
      return res.status(400).json({ message: 'Invalid username or password' });
    }

    // Compare the password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid username or password' });
    }

    // Generate a JWT
    const token = jwt.sign(
      { userId: user._id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.status(200).json({
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isPremium: user.isPremium,
        premiumLevel: user.premiumLevel
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/register', async (req, res) => {
  await dbConnect();

  try {
    const {
      firstName,
      lastName,
      email,
      password,
      grade,
      school,
      parentEmail,
      parentPhone,
      country,
      emirate,
      state
    } = req.body;

    // Check if the user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create username from email
    const username = email;

    // Create a new user
    const user = new User({
      username,
      firstName,
      lastName,
      email,
      password: hashedPassword,
      grade,
      school,
      parentEmail,
      parentPhone,
      country: country || 'United Arab Emirates',
      emirate,
      state
    });

    await user.save();

    // Generate a JWT for the new user
    const token = jwt.sign(
      { userId: user._id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Return the token and success message
    res.status(201).json({
      token,
      message: 'User registered successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isPremium: user.isPremium,
        premiumLevel: user.premiumLevel
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Legacy verify-token route
app.get('/api/verify-token', async (req, res) => {
  await dbConnect();

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({
      isPremium: user.isPremium,
      premiumLevel: user.premiumLevel,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName
      }
    });
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({ message: 'Invalid token' });
  }
});

// Legacy process-payment route
app.post('/api/process-payment', async (req, res) => {
  await dbConnect();

  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided or invalid token format' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { level, amount } = req.body;

    if (!level) {
      return res.status(400).json({ message: 'Level and amount are required' });
    }

    // Check if we're in test mode (for quick testing without PayCaps)
    if (process.env.PAYCAPS_APP_ID === 'TEST_MODE') {
      // Test mode - simulate successful payment
      try {
        await User.findByIdAndUpdate(decoded.userId, {
          isPremium: true,
          premiumLevel: level,
          pendingOrderId: null,
          pendingPremiumLevel: null,
        });

        return res.status(200).json({
          message: 'Payment successful (Test Mode)',
          testMode: true,
          level: level,
          redirect: `${process.env.FRONTEND_URL}/payment/success?testMode=true`
        });

      } catch (error) {
        console.error('Test payment failed:', error);
        return res.status(500).json({ message: 'Test payment failed', error: error.message });
      }
    }

    // Check if PayCaps credentials are configured
    if (!process.env.PAYCAPS_APP_ID || process.env.PAYCAPS_APP_ID === 'your_actual_app_id_from_dashboard') {
      return res.status(500).json({
        message: 'PayCaps credentials not configured. Please update PAYCAPS_APP_ID and PAYCAPS_SECRET in .env file.',
        error: 'PayCaps credentials not set up',
        instructions: 'Get your APP_ID and SECRET from PayCaps dashboard at https://sandbox.paycaps.com/pgui/jsp/index'
      });
    }

    // Paycaps payment integration here
    const orderId = `ORDER_${Date.now()}_${user._id.toString()}`;

    const paymentParams = {
      APP_ID: process.env.PAYCAPS_APP_ID,
      ORDER_ID: orderId,
      RETURN_URL: `${req.headers.origin}/api/payment/callback`,
      MERCHANTNAME: process.env.PAYCAPS_MERCHANT_NAME,
      AMOUNT: amount * 100, // Amount in phils/paisa/cents
      CURRENCY_CODE: 784, // Numerical code for AED as in paycaps documentation
      TXNTYPE: 'SALE',
      CUST_NAME: `${user.firstName} ${user.lastName}` || 'test_name',
      CUST_EMAIL: user.email || '<EMAIL>',
      CUST_PHONE: user.parentPhone || 526395565,
      CUST_CITY: user.emirate || 'test_city',
      CUST_COUNTRY: user.country || 'United Arab Emirates',
      CUST_ZIP: user.zip || 123,
    };

    const { getHashedString } = require('./src/utils/common');
    const hash = getHashedString(paymentParams);
    paymentParams.HASH = hash;

    try {
      await User.findByIdAndUpdate(decoded.userId, {
        pendingOrderId: orderId,
        pendingPremiumLevel: level,
      });

      return res.status(200).json({
        message: 'Payment initiated',
        paymentParams
      });

    } catch (error) {
      console.error('Payment initiation failed:', error);
      return res.status(500).json({ message: 'Payment initiation failed', error: error.message });
    }
  } catch (error) {
    console.error('Error during payment processing:', error);
    res.status(500).json({ message: 'Payment failed', error: error.message });
  }
});

// Progress tracking routes
app.get('/api/progress', async (req, res) => {
  await dbConnect();

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({
      progress: user.progress || {
        currentLevel: 'level1',
        currentLesson: '1.1WhatIsComputer',
        completedLessons: [],
        lastAccessedAt: new Date(),
        totalLessonsCompleted: 0,
        progressPercentage: 0
      }
    });
  } catch (error) {
    console.error('Error fetching progress:', error);
    res.status(500).json({ message: 'Error fetching progress', error: error.message });
  }
});

app.post('/api/progress/update', async (req, res) => {
  await dbConnect();

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const { lessonId, level, action } = req.body;

    if (!lessonId || !level) {
      return res.status(400).json({ message: 'Lesson ID and level are required' });
    }

    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Initialize progress if it doesn't exist
    if (!user.progress) {
      user.progress = {
        currentLevel: 'level1',
        currentLesson: '1.1WhatIsComputer',
        completedLessons: [],
        lastAccessedAt: new Date(),
        totalLessonsCompleted: 0,
        progressPercentage: 0
      };
    }

    if (action === 'complete') {
      // Mark lesson as completed
      if (!user.progress.completedLessons.includes(lessonId)) {
        user.progress.completedLessons.push(lessonId);
        user.progress.totalLessonsCompleted = user.progress.completedLessons.length;
      }
    } else if (action === 'access') {
      // Update current lesson and last accessed time
      user.progress.currentLevel = level;
      user.progress.currentLesson = lessonId;
    }

    user.progress.lastAccessedAt = new Date();

    // Calculate progress percentage (assuming 100 total lessons across all levels)
    const totalLessons = 100; // You can adjust this based on actual lesson count
    user.progress.progressPercentage = Math.round((user.progress.totalLessonsCompleted / totalLessons) * 100);

    await user.save();

    res.status(200).json({
      message: 'Progress updated successfully',
      progress: user.progress
    });
  } catch (error) {
    console.error('Error updating progress:', error);
    res.status(500).json({ message: 'Error updating progress', error: error.message });
  }
});

// Legacy payment callback route
app.post('/api/payment-callback', async (req, res) => {
  await dbConnect();

  try {
    const {
      APP_ID,
      ORDER_ID,
      AMOUNT,
      CURRENCY_CODE,
      TXNTYPE,
      CUST_NAME,
      CUST_EMAIL,
      CUST_PHONE,
      CUST_CITY,
      CUST_COUNTRY,
      CUST_ZIP,
      MERCHANTNAME,
      RETURN_URL,
      RESPONSE_CODE,
      RESPONSE_MESSAGE,
    } = req.body;

    const responseParams = {
      APP_ID,
      ORDER_ID,
      AMOUNT,
      CURRENCY_CODE,
      TXNTYPE,
      CUST_NAME,
      CUST_EMAIL,
      CUST_PHONE,
      CUST_CITY,
      CUST_COUNTRY,
      CUST_ZIP,
      MERCHANTNAME,
      RETURN_URL
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('Payment Callback Response:\n', JSON.stringify(responseParams, null, 3));
    }

    const user = await User.findOne({ pendingOrderId: ORDER_ID });
    console.log('User found for order:', ORDER_ID, user ? 'Yes' : 'No');

    if (!user) {
      return res.redirect(302, `${process.env.FRONTEND_URL}/payment/failed?orderId=${ORDER_ID}&error=user_not_found`);
    }

    if (RESPONSE_CODE === '000') {
      // Payment successful
      user.isPremium = true;
      user.premiumLevel = user.pendingPremiumLevel;
      user.pendingOrderId = null;
      user.pendingPremiumLevel = null;
      await user.save();

      console.log('Payment successful for user:', user.email, 'Level:', user.premiumLevel);
      return res.redirect(302, `${process.env.FRONTEND_URL}/payment/success?orderId=${ORDER_ID}&level=${user.premiumLevel}`);
    } else {
      // Payment failed
      user.pendingOrderId = null;
      user.pendingPremiumLevel = null;
      await user.save();

      console.log('Payment failed for user:', user.email, 'Response:', RESPONSE_MESSAGE);
      return res.redirect(302, `${process.env.FRONTEND_URL}/payment/failed?orderId=${ORDER_ID}&message=${encodeURIComponent(RESPONSE_MESSAGE)}`);
    }
  } catch (error) {
    console.error('Error processing payment callback:', error);
    return res.status(500).json({ message: 'Error processing payment callback', error: error.message });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'CodeSprint Backend API is running',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : {}
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
});

module.exports = app;
