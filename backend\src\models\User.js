const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
  },
  firstName: {
    type: String,
    required: true,
  },
  lastName: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  password: {
    type: String,
    required: true,
  },
  grade: {
    type: String,
    required: true,
  },
  school: {
    type: String,
    required: true,
  },
  parentEmail: {
    type: String,
    required: true,
  },
  parentPhone: {
    type: String,
    required: true,
  },
  country: {
    type: String,
    default: 'United Arab Emirates',
  },
  emirate: {
    type: String,
    required: true,
  },
  state: {
    type: String,
  },
  isPremium: {
    type: Boolean,
    default: false,
  },
  premiumLevel: {
    type: String,
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5'],
    default: null,
  },
  pendingOrderId: {
    type: String,
    default: null,
  },
  pendingPremiumLevel: {
    type: String,
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5'],
    default: null,
  }
}, {
  timestamps: true
});

module.exports = mongoose.models.User || mongoose.model('User', UserSchema);
