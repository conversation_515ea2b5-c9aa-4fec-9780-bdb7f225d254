/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./src/pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.js\");\n/* harmony import */ var _src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/index.jsx */ \"(pages-dir-node)/./src/pages/index.jsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_index_jsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lessons/level-4/Section1/animations.module.css */ \"(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\");\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n// pages/_app.js\n\n // Global styles\n // Animations CSS\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_app.js\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n// Add PropTypes for App\nApp.propTypes = {\n    Component: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().elementType).isRequired,\n    pageProps: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object).isRequired\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjs7QUFDYyxDQUFDLGdCQUFnQjtBQUNXLENBQUMsaUJBQWlCO0FBQ3pDO0FBRXBCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0FBRUEsd0JBQXdCO0FBQ3hCRixJQUFJRyxTQUFTLEdBQUc7SUFDZEYsV0FBV0YsK0RBQXFCLENBQUNNLFVBQVU7SUFDM0NILFdBQVdILDBEQUFnQixDQUFDTSxVQUFVO0FBQ3hDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWdlcy9fYXBwLmpzXG5pbXBvcnQgXCJAL3N0eWxlcy9nbG9iYWxzLmNzc1wiOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgXCIuL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3NcIjsgLy8gQW5pbWF0aW9ucyBDU1NcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSBcInByb3AtdHlwZXNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cblxuLy8gQWRkIFByb3BUeXBlcyBmb3IgQXBwXG5BcHAucHJvcFR5cGVzID0ge1xuICBDb21wb25lbnQ6IFByb3BUeXBlcy5lbGVtZW50VHlwZS5pc1JlcXVpcmVkLCAvLyBDb21wb25lbnQgbXVzdCBiZSBhIHZhbGlkIFJlYWN0IGNvbXBvbmVudFxuICBwYWdlUHJvcHM6IFByb3BUeXBlcy5vYmplY3QuaXNSZXF1aXJlZCwgLy8gcGFnZVByb3BzIGlzIGFuIG9iamVjdFxufTtcbiJdLCJuYW1lcyI6WyJQcm9wVHlwZXMiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwcm9wVHlwZXMiLCJlbGVtZW50VHlwZSIsImlzUmVxdWlyZWQiLCJvYmplY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_document.js":
/*!********************************!*\
  !*** ./src/pages/_document.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL19kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZCAvPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/index.jsx":
/*!*****************************!*\
  !*** ./src/pages/index.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/Home.module.css */ \"(pages-dir-node)/./src/styles/Home.module.css\");\n/* harmony import */ var _styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: \"/codesprint-logo.png\",\n                            alt: \"CodeSprint Logo\",\n                            width: 150,\n                            height: 50\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().nav),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/courses\",\n                                children: \"Courses\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/faq\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                children: \"Contact Us\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/login\",\n                        passHref: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().loginButton),\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().main),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().content),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: [\n                                    \"CODE SPRINT\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 24\n                                    }, this),\n                                    \"BLAH BLAH\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().description),\n                                children: \"Code Sprint offers an engaging AI-based curriculum for students in grades 1 to 10, covering AI, data science, cybersecurity, blockchain, and more. Through interactive courses, hands-on projects, and fun competitions, we inspire the next generation of tech-savvy problem solvers. Join us in making learning an exciting adventure!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/register\",\n                                passHref: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().registerButton),\n                                    children: \"REGISTER NOW!\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().mascot),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: \"/glitch-mascot.png\",\n                                alt: \"Glitch Mascot\",\n                                width: 400,\n                                height: 400\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().meetGlitch),\n                                children: \"MEET GLITCH\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().ourMascot),\n                                children: \"OUR MASCOT!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().coursesSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().coursesIntro),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: [\n                                    \"CHECK OUT OUR \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().highlightedText),\n                                        children: \"COURSES\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 38\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"This is a paragraph about the courses or something like that. It is a paragraph that is going to be at least one or two lines. This is a paragraph about the courses or something, but it is a paragraph that is going to be at least one or two lines.\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"It is a paragraph that is going to be at least one or two lines. This is a paragraph about the courses or something, but it is a paragraph that is going to be at least one or two lines.\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: \"/glitch-mascot.png\",\n                                alt: \"Mascot\",\n                                width: 120,\n                                height: 160,\n                                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().mascotImage)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().coursesGrid),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/level1\",\n                                passHref: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().courseBox)} ${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().purpleBox)}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"name of level 1\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/glitch-mascot.png\",\n                                            alt: \"Level 1 Icon\",\n                                            width: 150,\n                                            height: 50\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().addIcon),\n                                            children: \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/level2\",\n                                passHref: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().courseBox)} ${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().yellowBox)}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"name of level 2\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/glitch-mascot.png\",\n                                            alt: \"Level 2 Icon\",\n                                            width: 150,\n                                            height: 50\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().addIcon),\n                                            children: \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/level3\",\n                                passHref: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().courseBox)} ${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().redBox)}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"name of level 3\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/glitch-mascot.png\",\n                                            alt: \"Level 3 Icon\",\n                                            width: 150,\n                                            height: 50\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().addIcon),\n                                            children: \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().courseBox)} ${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().blueBox)}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"name of level 3\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: \"/glitch-mascot.png\",\n                                        alt: \"Level 4 Icon\",\n                                        width: 150,\n                                        height: 50\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/level5\",\n                                passHref: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().courseBox)} ${(_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().orangeBox)}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"name of level 4\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/glitch-mascot.png\",\n                                            alt: \"Level 5 Icon\",\n                                            width: 150,\n                                            height: 50\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().addIcon),\n                                            children: \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().aboutSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().aboutContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().aboutTitle),\n                            children: \"ABOUT US\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().aboutBox),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().aboutText),\n                                    children: \"This is a paragraph about the course or something like that. It is a paragraph that is going to be at least one or two lines. This is a paragraph about the courses or something, but it is a paragraph that is going to be at least one or two lines. It is a paragraph that is going to be at least one or two lines. This is a paragraph about the courses or something, but it is a paragraph that is going to be at least one or two lines. This is a paragraph about the course or something like that. It is a paragraph that is going to be at least one or two lines. This is a paragraph about the courses or something, but it is a paragraph that is going to be at least one or two lines.\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().mascotContainer),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: \"/glitch-mascot.png\",\n                                        alt: \"Glitch Mascot\",\n                                        width: 200,\n                                        height: 200,\n                                        className: (_styles_Home_module_css__WEBPACK_IMPORTED_MODULE_3___default().aboutMascot)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/index.jsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/index.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css":
/*!******************************************************************!*\
  !*** ./src/pages/lessons/level-4/Section1/animations.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"confetti\": \"animations_confetti__VOjD6\",\n\t\"fadeInUp\": \"animations_fadeInUp__wib6A\",\n\t\"hidden\": \"animations_hidden__KLYnx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29uZmV0dGlcIjogXCJhbmltYXRpb25zX2NvbmZldHRpX19WT2pENlwiLFxuXHRcImZhZGVJblVwXCI6IFwiYW5pbWF0aW9uc19mYWRlSW5VcF9fd2liNkFcIixcblx0XCJoaWRkZW5cIjogXCJhbmltYXRpb25zX2hpZGRlbl9fS0xZbnhcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/Home.module.css":
/*!************************************!*\
  !*** ./src/styles/Home.module.css ***!
  \************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"Home_container__9OuOz\",\n\t\"header\": \"Home_header__ZUWxe\",\n\t\"logo\": \"Home_logo__ZEOng\",\n\t\"nav\": \"Home_nav__ZMqH2\",\n\t\"loginButton\": \"Home_loginButton__SXCRR\",\n\t\"main\": \"Home_main__2uIek\",\n\t\"content\": \"Home_content__Qnbja\",\n\t\"title\": \"Home_title__YEn0u\",\n\t\"description\": \"Home_description__zHUB6\",\n\t\"registerButton\": \"Home_registerButton__gcwdh\",\n\t\"mascot\": \"Home_mascot__ZLk1L\",\n\t\"coursesSection\": \"Home_coursesSection__6U7DD\",\n\t\"coursesHeader\": \"Home_coursesHeader__n6IQ8\",\n\t\"coursesTitle\": \"Home_coursesTitle__aWuWS\",\n\t\"coursesContent\": \"Home_coursesContent__wSP_q\",\n\t\"coursesIntro\": \"Home_coursesIntro__PIF6w\",\n\t\"coursesGrid\": \"Home_coursesGrid__5Uese\",\n\t\"courseBox\": \"Home_courseBox__N2ucD\",\n\t\"orangeBox\": \"Home_orangeBox__U7cE7\",\n\t\"blueBox\": \"Home_blueBox__9VyO6\",\n\t\"aboutSection\": \"Home_aboutSection__i_8Gi\",\n\t\"aboutTitle\": \"Home_aboutTitle__uEYta\",\n\t\"aboutContent\": \"Home_aboutContent__XEdKA\",\n\t\"aboutText\": \"Home_aboutText__2U8C6\",\n\t\"aboutMascot\": \"Home_aboutMascot__SGorZ\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/styles/Home.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();