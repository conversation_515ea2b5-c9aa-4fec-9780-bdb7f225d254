{"c": ["webpack"], "r": ["pages/index", "pages/go-premium"], "m": ["(pages-dir-browser)/../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[8].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[8].use[2]!./src/styles/Home.module.css", "(pages-dir-browser)/../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fmnt%2Fd%2FAamir%2Fcodesprint%2Fcode_sprint%2Ffrontend%2Fsrc%2Fpages%2Findex.jsx&page=%2F!", "(pages-dir-browser)/./src/pages/index.jsx", "(pages-dir-browser)/./src/styles/Home.module.css", "(pages-dir-browser)/../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[8].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[8].use[2]!./src/styles/Premium.module.css", "(pages-dir-browser)/../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fmnt%2Fd%2FAamir%2Fcodesprint%2Fcode_sprint%2Ffrontend%2Fsrc%2Fpages%2Fgo-premium.jsx&page=%2Fgo-premium!", "(pages-dir-browser)/./src/pages/go-premium.jsx", "(pages-dir-browser)/./src/styles/Premium.module.css"]}