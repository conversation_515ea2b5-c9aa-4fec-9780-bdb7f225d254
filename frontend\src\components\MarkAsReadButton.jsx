import React, { useState, useEffect } from 'react';
import { markLessonComplete, isLessonCompleted, getUserProgress } from '../utils/progressTracker';

const MarkAsReadButton = ({ lessonId, level, onComplete }) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [completedLessons, setCompletedLessons] = useState([]);

  useEffect(() => {
    // Check if lesson is already completed
    const checkCompletion = async () => {
      try {
        const progress = await getUserProgress();
        setCompletedLessons(progress.completedLessons || []);
        setIsCompleted(isLessonCompleted(lessonId, progress.completedLessons || []));
      } catch (error) {
        console.error('Error checking lesson completion:', error);
      }
    };

    checkCompletion();
  }, [lessonId]);

  const handleMarkAsRead = async () => {
    if (isCompleted) return; // Already completed

    setIsLoading(true);
    try {
      const updatedProgress = await markLessonComplete(lessonId, level);
      setIsCompleted(true);
      setCompletedLessons(updatedProgress.completedLessons || []);
      
      // Call onComplete callback if provided
      if (onComplete) {
        onComplete(updatedProgress);
      }

      // Show success message
      alert('Lesson marked as complete! 🎉');
    } catch (error) {
      console.error('Error marking lesson as complete:', error);
      alert('Failed to mark lesson as complete. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex justify-center mt-6">
      <button
        onClick={handleMarkAsRead}
        disabled={isCompleted || isLoading}
        className={`
          px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105
          ${isCompleted 
            ? 'bg-green-500 cursor-not-allowed' 
            : 'bg-blue-500 hover:bg-blue-600 active:scale-95'
          }
          ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>Updating...</span>
          </div>
        ) : isCompleted ? (
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <span>Completed</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Mark as Read</span>
          </div>
        )}
      </button>
    </div>
  );
};

export default MarkAsReadButton;
