import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { getUserProgress, getNextLesson, getLessonDisplayName } from '../utils/progressTracker';

const ProgressTracker = () => {
  const [progress, setProgress] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchProgress = async () => {
      try {
        const userProgress = await getUserProgress();
        setProgress(userProgress);
      } catch (error) {
        console.error('Error fetching progress:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProgress();
  }, []);

  const handleContinueLearning = () => {
    if (!progress) return;

    const nextLesson = getNextLesson(progress.currentLevel, progress.currentLesson);
    
    if (nextLesson) {
      // Navigate to the next lesson
      router.push(`/lessons/${nextLesson.level}/${nextLesson.lesson}`);
    } else {
      // Navigate to current lesson if no next lesson
      router.push(`/lessons/${progress.currentLevel}/${progress.currentLesson}`);
    }
  };

  const handleResumeFromLastPosition = () => {
    if (!progress) return;
    
    // Navigate to the current lesson (where they left off)
    router.push(`/lessons/${progress.currentLevel}/${progress.currentLesson}`);
  };

  if (loading) {
    return (
      <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]">
        <h2 className="text-2xl font-bold mb-4 text-[#FFA500]">PROGRESS</h2>
        <div className="flex justify-center items-center h-48">
          <div className="w-8 h-8 border-4 border-[#FFA500] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (!progress) {
    return (
      <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]">
        <h2 className="text-2xl font-bold mb-4 text-[#FFA500]">PROGRESS</h2>
        <p className="text-white text-center">Unable to load progress</p>
      </div>
    );
  }

  const progressPercentage = progress.progressPercentage || 0;
  const nextLesson = getNextLesson(progress.currentLevel, progress.currentLesson);

  return (
    <div className="space-y-6">
      {/* Progress Circle */}
      <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]">
        <h2 className="text-2xl font-bold mb-4 text-[#FFA500]">PROGRESS</h2>
        
        {/* Circular Progress */}
        <div className="w-48 h-48 mx-auto relative">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            {/* Background circle */}
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="#FFA500"
              strokeWidth="8"
              fill="none"
              opacity="0.3"
            />
            {/* Progress circle */}
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="#FFA500"
              strokeWidth="8"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 40}`}
              strokeDashoffset={`${2 * Math.PI * 40 * (1 - progressPercentage / 100)}`}
              className="transition-all duration-500 ease-out"
            />
          </svg>
          
          {/* Progress text in center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-3xl font-bold text-[#FFA500]">{progressPercentage}%</div>
              <div className="text-sm text-white">Complete</div>
            </div>
          </div>
        </div>

        {/* Progress Stats */}
        <div className="mt-4 text-center">
          <p className="text-white text-sm">
            <span className="font-semibold text-[#FFA500]">{progress.totalLessonsCompleted}</span> lessons completed
          </p>
          <p className="text-white text-xs mt-1">
            Last accessed: {new Date(progress.lastAccessedAt).toLocaleDateString()}
          </p>
        </div>
      </div>

      {/* Current Lesson Info */}
      <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-2 border-[#FFA500]">
        <h3 className="text-xl font-bold mb-3 text-[#FFA500]">CURRENT LESSON</h3>
        <div className="bg-[#00264D] rounded-xl p-4">
          <p className="text-white font-medium">
            {getLessonDisplayName(progress.currentLesson)}
          </p>
          <p className="text-[#B0C4DE] text-sm mt-1">
            {progress.currentLevel.toUpperCase()}
          </p>
        </div>
        
        <button
          onClick={handleResumeFromLastPosition}
          className="w-full mt-4 bg-[#FFA500] text-[#003366] py-2 px-4 rounded-lg font-semibold hover:bg-[#FF8C00] transition-colors duration-200"
        >
          Resume Learning
        </button>
      </div>

      {/* Next Lesson */}
      {nextLesson && (
        <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-2 border-[#00FF00]">
          <h3 className="text-xl font-bold mb-3 text-[#00FF00]">NEXT LESSON</h3>
          <div className="bg-[#00264D] rounded-xl p-4">
            <p className="text-white font-medium">
              {getLessonDisplayName(nextLesson.lesson)}
            </p>
            <p className="text-[#B0C4DE] text-sm mt-1">
              {nextLesson.level.toUpperCase()}
            </p>
          </div>
          
          <button
            onClick={handleContinueLearning}
            className="w-full mt-4 bg-[#00FF00] text-[#003366] py-2 px-4 rounded-lg font-semibold hover:bg-[#00CC00] transition-colors duration-200"
          >
            Continue Learning
          </button>
        </div>
      )}

      {/* Completion Message */}
      {!nextLesson && progressPercentage === 100 && (
        <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-2 border-[#FFD700]">
          <h3 className="text-xl font-bold mb-3 text-[#FFD700]">🎉 CONGRATULATIONS!</h3>
          <p className="text-white text-center">
            You've completed all available lessons!
          </p>
        </div>
      )}
    </div>
  );
};

export default ProgressTracker;
