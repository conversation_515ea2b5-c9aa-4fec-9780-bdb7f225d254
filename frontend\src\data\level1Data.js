import React from "react";
import WhatIsComputer from "../pages/lessons/level1/1.1WhatIsComputer";
import HowComputersWork from "../pages/lessons/level1/1.2HowComputersWork";
import WhatIsAProgram from "../pages/lessons/level1/1.3WhatIsAProgram";
import TypesOfComputers from "../pages/lessons/level1/1.4TypesOfComputers";
import HardwareAndSoftware from "../pages/lessons/level1/1.5HardwareAndSoftware";
import OperatingSystem from "../pages/lessons/level1/1.6OperatingSystem";
import WhatIsScratch from "../pages/lessons/level1/2.1WhatIsScratch";
// import NavigatingScratch from "../pages/lessons/level1/2.2NavigatingScratch";
// import FirstScratchProject from "../pages/lessons/level1/2.3FirstScratchProject";
import WhatIsIoT from "../pages/lessons/level1/3.1WhatIsIoT";
import HowIoTWorks from "../pages/lessons/level1/3.2HowIoTWorks";
import IoTExamples from "../pages/lessons/level1/3.3IoTExamples";
import IoTBenefits from "../pages/lessons/level1/3.4IoTBenefits";
import WhatIsComputerVision from "../pages/lessons/level1/4.1WhatIsComputerVision";
import HowComputerVisionWorks from "../pages/lessons/level1/4.2HowComputerVisionWorks";
import ComputerVisionApplications from "../pages/lessons/level1/4.3ComputerVisionApplications";
import Summary from "../pages/lessons/level1/5.1Summary";
import CompletionMessage from "../pages/lessons/level1/5.3CompletionMessage";

const level1Data = {
  majorTopics: [
    {
      title: "Introduction to Computers",
      minorTopics: [
        { title: "What is a Computer?", component: <WhatIsComputer /> },
        { title: "How Computers Work", component: <HowComputersWork /> },
        { title: "What is a Computer Program?", component: <WhatIsAProgram /> },
        { title: "Different Types of Computers", component: <TypesOfComputers /> },
        { title: "Hardware vs. Software", component: <HardwareAndSoftware /> },
        { title: "What is an Operating System?", component: <OperatingSystem /> },
      ],
    },
    {
      title: "Getting Started with Scratch",
      minorTopics: [
        { title: "What is Scratch?", component: <WhatIsScratch /> },
        // { title: "Navigating the Scratch Interface", component: <NavigatingScratch /> },
        // { title: "Creating Your First Project", component: <FirstScratchProject /> },
      ],
    },
    {
      title: "Introduction to IoT",
      minorTopics: [
        { title: "What is IoT?", component: <WhatIsIoT /> },
        { title: "How IoT Works", component: <HowIoTWorks /> },
        { title: "Examples of IoT Devices", component: <IoTExamples /> },
        { title: "Benefits of IoT", component: <IoTBenefits /> },
      ],
    },
    {
    title: "Introduction to Computer Vision",
      minorTopics: [
        { title: "What is Computer Vision?", component: <WhatIsComputerVision /> },
        { title: "How Does Computer Vision Work?", component: <HowComputerVisionWorks /> },
        { title: "Applications of Computer Vision", component: <ComputerVisionApplications /> },
      ],
    },
    {
        title: "Conclusion and Review",
        minorTopics: [
          { title: "Summary of Key Concepts", component: <Summary /> },
          { title: "Course Completion Message", component: <CompletionMessage /> },
        ],
      },
      
  ],
};

export default level1Data;
