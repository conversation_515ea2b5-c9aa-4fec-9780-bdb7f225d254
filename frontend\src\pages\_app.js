// pages/_app.js
import "@/styles/globals.css"; // Global styles
import "./lessons/level-4/Section1/animations.module.css"; // Animations CSS
import PropTypes from "prop-types";

export default function App({ Component, pageProps }) {
  return <Component {...pageProps} />;
}

// Add PropTypes for App
App.propTypes = {
  Component: PropTypes.elementType.isRequired, // Component must be a valid React component
  pageProps: PropTypes.object.isRequired, // pageProps is an object
};
