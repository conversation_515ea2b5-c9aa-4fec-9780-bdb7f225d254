import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import styles from '../styles/courses.module.css'
import PropTypes from 'prop-types'; // Import PropTypes


const CourseCard = ({ level, color, icon, href }) => (
  <Link href={href} className={`${styles.courseCard} ${styles[color]}`}>
    <h3>name of level {level}</h3>
    <Image src={icon} alt={`Level ${level} icon`} width={100} height={100} />
    <div className={styles.plusIcon}>+</div>
  </Link>
)

// PropTypes for CourseCard
CourseCard.propTypes = {
  level: PropTypes.number.isRequired, // Ensures level is a required number
  color: PropTypes.string.isRequired, // Ensures color is a required string
  icon: PropTypes.string.isRequired,  // Ensures icon is a required string
  href: PropTypes.string.isRequired,  // Ensures href is a required string
};

export default function Courses() {
  return (
    <div className={styles.container}>
      <h1 className={styles.title}>CHECK OUT OUR <span>COURSES</span></h1>
      
      <div className={styles.description}>
        <p>
          This is a paragraph about the course or something but it is a paragraph that is gonna be at
          least one or two lines. This is a paragraph about the course or something but it is a paragraph
          that is gonna be at least one or two lines. This is a paragraph about the course or something but
          it is a paragraph that is gonna be at least one or two lines. This is a paragraph about the
          course or something but it is a paragraph that is gonna be at least one or two lines.
        </p>
      </div>

      <div className={styles.mascot}>
        <Image src="/glitch-mascot.png" alt="Glitch Mascot" width={200} height={200} />
      </div>

      <div className={styles.courseGrid}>
        <CourseCard level={1} color="purple" icon="/level1-icon.png" href="/courses/level1" />
        <CourseCard level={2} color="yellow" icon="/level2-icon.png" href="/courses/level2" />
        <CourseCard level={3} color="orange" icon="/level3-icon.png" href="/courses/level3" />
        <CourseCard level={4} color="coral" icon="/level4-icon.png" href="/courses/level4" />
        <CourseCard level={5} color="red" icon="/level5-icon.png" href="/courses/level5" />
      </div>
    </div>
  )
}