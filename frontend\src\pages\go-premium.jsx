import { useState } from 'react';
import styles from '../styles/Premium.module.css';

export default function GoPremium() {
  const [selectedLevel, setSelectedLevel] = useState('');

  const handleLevelChange = (e) => {
    setSelectedLevel(e.target.value);
  };

  const handlePayment = async () => {
    const token = localStorage.getItem('token');

    const res = await fetch('/api/process-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ level: selectedLevel, amount: 250 }),
    });

    if (res.status === 200) {
      const data = await res.json();
      if (data.paymentParams) {
        // Create a form element for the payment gateway
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = process.env.NEXT_PUBLIC_PAYCAPS_PAYMENT_REQUEST_URL;
        
        Object.entries(data.paymentParams).forEach(([key, value]) => {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = value;
          form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
      } else {
        alert('Payment initialization failed. Please try again.');
      }
    } else {
      alert('Payment failed. Please try again.');
    }
  };

  return (
    <div className={styles.premiumContainer}>
      <div className={styles.card}>
        <h1 className={styles.title}>Go Premium</h1>
        <p className={styles.description}>Choose your level to unlock premium content:</p>
        <select value={selectedLevel} onChange={handleLevelChange} className={styles.select}>
          <option value="">Select a level</option>
          <option value="Level 1">Level 1 (Grades 1-2)</option>
          <option value="Level 2">Level 2 (Grades 3-4)</option>
          <option value="Level 3">Level 3 (Grades 5-6)</option>
          <option value="Level 4">Level 4 (Grades 7-8)</option>
          <option value="Level 5">Level 5 (Grades 9-10)</option>
        </select>
        <button onClick={handlePayment} className={styles.payButton} disabled={!selectedLevel}>
          Pay Now
        </button>
      </div>
    </div>
  );
}
