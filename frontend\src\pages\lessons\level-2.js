import React, { useState, useEffect } from "react";
import Link from "next/link";
import { level2Data } from "./level2/0.0CourseContent"; // Import level2Data
import DisplayLevel2 from "./level2/0.1DisplayLevel2";

export default function Level2() {
  const [currentMajorTopic, setCurrentMajorTopic] = useState(null);
  const [currentMinorTopic, setCurrentMinorTopic] = useState(null);
  const [shakeClass, setShakeClass] = useState("");

  // Load external Spline script
  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://unpkg.com/@splinetool/viewer@1.9.46/build/spline-viewer.js";
    script.type = "module";
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  // Shake effect when switching topics
  useEffect(() => {
    if (currentMajorTopic !== null && currentMinorTopic !== null) {
      setShakeClass("shake");
      const timeout = setTimeout(() => setShakeClass(""), 500);
      return () => clearTimeout(timeout);
    }
  }, [currentMajorTopic, currentMinorTopic]);

  // Handle minor topic selection
  const handleMinorTopicClick = (majorIndex, minorIndex) => {
    setCurrentMajorTopic(majorIndex);
    setCurrentMinorTopic(minorIndex);
  };

  // Reset to main course content
  const handleBackClick = () => {
    setCurrentMajorTopic(null);
    setCurrentMinorTopic(null);
  };

  // Get the currently selected topic's content
  const currentTopic =
    level2Data.majorTopics[currentMajorTopic]?.minorTopics[currentMinorTopic];
  const CurrentLessonComponent = currentTopic?.component;

  return (
    <div className="flex min-h-screen bg-[#FFF6E5] overflow-hidden">
      {/* Sidebar */}
      <div className="w-1/4 bg-[#5E2C99] p-6 text-white fixed top-0 left-0 h-screen flex flex-col justify-between overflow-hidden">
        <h2 className="text-3xl font-bold mb-8 text-center">Course Progress</h2>
        <ul className="space-y-6 text-lg font-semibold">
          {currentMajorTopic === null ? (
            level2Data.majorTopics.map((major, majorIndex) => (
              <li key={majorIndex} className="w-full">
                <div className="flex items-center justify-between">
                  <h3
                    className="text-2xl font-bold mb-4 cursor-pointer"
                    onClick={() => handleMinorTopicClick(majorIndex, 0)}
                  >
                    {major.title.split(" ").map((word, index) => (
                      <span key={index} className="falling-word">
                        {word}&nbsp;
                      </span>
                    ))}
                  </h3>
                </div>
              </li>
            ))
          ) : (
            <li className="w-full">
              <div className="flex items-center">
                <span
                  className="text-4xl cursor-pointer text-white mr-2 text-yellow-400"
                  onClick={handleBackClick}
                  style={{ marginTop: "-24px" }}
                >
                  «
                </span>
                <h3
                  className="text-2xl font-bold mb-4 text-yellow-400 cursor-pointer"
                  onClick={handleBackClick}
                >
                  {level2Data.majorTopics[currentMajorTopic]?.title}
                </h3>
              </div>
              <ul className="space-y-4 pl-4">
                {level2Data.majorTopics[currentMajorTopic]?.minorTopics.map(
                  (minor, minorIndex) => (
                    <li
                      key={minorIndex}
                      className={`subtitle cursor-pointer py-2 px-4 rounded-lg transition-all ${
                        currentMinorTopic === minorIndex
                          ? "bg-yellow-500 text-white"
                          : "bg-gray-500 text-white hover:bg-gray-400"
                      }`}
                      onClick={() =>
                        handleMinorTopicClick(currentMajorTopic, minorIndex)
                      }
                    >
                      {minor.title}
                    </li>
                  )
                )}
              </ul>
            </li>
          )}
        </ul>

        <Link href="/dashboard" passHref>
          <button className="bg-[#4CAF50] text-white rounded-lg px-4 py-2 mt-4 w-full hover:bg-green-600 transition-all">
            Back to Dashboard
          </button>
        </Link>
      </div>

      {/* Main Content */}
      <div className={`flex-1 overflow-auto ml-[25%]`}>
        {currentMajorTopic === null && currentMinorTopic === null ? (
          <div style={{ position: "relative", overflow: "hidden" }}>
            {/* Render main display content when no topic is selected */}
            <DisplayLevel2 />
          </div>
        ) : (
          <div style={{ position: "relative", overflow: "hidden" }}>
            <div
              className={`content-container ${shakeClass}`}
              style={{ position: "relative" }}
            >
              {/* Render the selected lesson content */}
              {CurrentLessonComponent}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
