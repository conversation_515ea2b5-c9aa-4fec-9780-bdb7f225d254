import React, { useState, useRef } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";


const DataTypesAndVariables = () => {
  const [code, setCode] = useState(`# Python Data Types and Variables
# Declare a string
name = 'Erling Haaland'

# Declare an integer
user_id = 113852870582

# Declare a float
progress = 2.50

# Declare another integer
exp = 70

# Declare an integer variable
x = 2

# Perform addition
result = exp + x

print("Name:", name)
print("User ID:", user_id)
print("Progress:", progress)
print("Experience + x:", result)
`);
  const animatedCode = useTypingAnimation(code);

  const typedText = useTypingAnimation("Fun with Python!", 100); 

  const [output, setOutput] = useState("");
  const [showConfetti, setShow<PERSON>onfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);

  const outputRef = useRef(null);

  const runCode = () => {
    try {
      const name = "Erling Haaland";
      const user_id = 113852870582;
      const progress = 2.5;
      const exp = 70;
      const x = 2;
      const result = exp + x;

      setOutput(
        `Name: ${name}\nUser ID: ${user_id}\nProgress: ${progress}\nExperience + x: ${result}`
      );
      setShowOutput(true);
      setShowConfetti(true);
  
      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
  
      setTimeout(() => {
        document.querySelector(`.${styles.confetti}`)?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
  
    } catch (error) {
      console.error(error);
      setOutput("Error: There was a problem running your code.");
      setShowOutput(true);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #FFEECC, #FFD6E8)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
          position: "relative",
          animation: "pop 1s ease-out",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #FF4081, #448AFF)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
            animation: "bounce 1.5s infinite",
          }}
        >
          {typedText}
        </h1>{" "}
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            textAlign: "center",
            fontSize: "1.2rem",
            color: "#555",
          }}
        >
          <p>
          Learn <strong>data types</strong> and <strong>variables</strong> with
          a smile!{" "}
          </p>
          <Player
            src="https://fonts.gstatic.com/s/e/notoemoji/latest/263a_fe0f/lottie.json"
            style={{ width: "24px", height: "24px", marginLeft: "8px" }}
            autoplay
            loop
          />
        </div>{" "}

        <p style={{ fontSize: "1rem", color: "#666", marginTop: "10px" }}>
          In programming, variables act as containers for storing data values.
          Each variable has a name and holds a value. 📦
        </p>
        <p style={{ fontSize: "1rem", color: "#666", marginTop: "10px" }}>
          A variable name can include letters, numbers, and the <code>_</code>{" "}
          underscore.
        </p>
        <p style={{ fontSize: "1rem", color: "#666", marginTop: "10px" }}>
          Here are some examples of valid variable names and values:
        </p>
        <pre
          style={{
            backgroundColor: "#FFF8E1",
            padding: "15px",
            borderRadius: "10px",
            textAlign: "left",
            color: "#444",
            marginTop: "10px",
            border: "1px solid #FFEE93",
          }}
        >
          {`name = 'Erling Haaland'
user_id = 113852870582
progress = 2.50
exp = 70
x = 2`}
        </pre>
        <p style={{ fontSize: "1rem", color: "#666", marginTop: "10px" }}>
          The equal sign <strong>=</strong> is an assignment operator. By saying{" "}
          <strong>x = 2</strong>, we assign the integer <strong>2</strong> to
          the variable <strong>x</strong>.
        </p>
        <hr style={{ margin: "20px 0", borderColor: "#ddd" }} />
        {/* <p
          style={{
            fontWeight: "bold",
            fontSize: "2.1rem",
            color: "#333",
            marginTop: "20px",
          }}
        >
          Instructions
        </p> */}
        <div
          style={{
            position: "relative", zIndex: 10,
            backgroundColor: "#283593",
            padding: "20px",
            borderRadius: "10px",
            marginTop: "20px",
            textAlign: "left",
          }}
        >
          <Editor
            value={animatedCode}
            onValueChange={(newCode) => setCode(newCode)}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#1A237E",
              color: "#FFFFFF",
              minHeight: "550px",
              borderRadius: "10px",
            }}
          />
        </div>

                <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative", zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#4CAF50",
                color: "#fff",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "81%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#F3E5F5",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#524C4F",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#444", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>

      </div>
    </div>
  );
};

export default DataTypesAndVariables;
