import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const AreYouACatPerson = () => {
  const [code] = useState(`What's your favorite animal?\n`);
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShow<PERSON>onfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [buttonClicked, setButtonClicked] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);

  const typedText = useTypingAnimation("Are You a Cat Person?", 100);
  const [animatedCode, setAnimatedCode] = useState("");

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);

  const resetState = () => {
    setUserInput("");
    setOutput("");
    setShowOutput(false);
    setButtonClicked(false);
    setShowConfetti(false);
    setAnimatedCode(`What's your favorite animal?\n`);
  };

  const runCode = () => {
    if (buttonClicked) {

      resetState();
      return;
    }
  
    const simulatedInput = userInput.trim().toLowerCase();
    let outputMessage = "";
  
    if (!simulatedInput) {

      outputMessage = "Error: Please write your favorite animal.";
    } else if (simulatedInput === "cat") {

      outputMessage = "Woohoo you are a cat person!";
      setShowConfetti(true);
    } else {

      outputMessage = "That's a very good animal, but it's not a cat.";
    }
  
    setOutput(outputMessage);
    setShowOutput(true);
    setButtonClicked(true);
  

    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  

    if (simulatedInput === "cat") {
      setTimeout(() => {
        document
          .querySelector(`.${styles.confetti}`)
          ?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
    }
  };
  
  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      const prompt = "What's your favorite animal?";
      console.log(prompt);
      const inputWithoutPrompt = lines.slice(1).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };
  
  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #B59F78, #4C1F7A)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #B59F78, #4C1F7A)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700">
          An <strong>if statement</strong> is used to test a condition for
          truth:
        </p>
        <p className="text-lg text-gray-700">
          If the condition evaluates to <strong>True</strong>, the code inside
          the <code className="font-mono text-blue-500">if</code> block is
          executed. If the condition evaluates to <strong>False</strong>, the
          code inside the <code className="font-mono text-blue-500">if</code>{" "}
          block is skipped.
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
          if is_weekend: {"\n"}
          {"    "}print(&quot;Time to relax!&quot;)
        </pre>
        <p className="mt-4 text-lg text-gray-700">
          <strong>Else</strong>
        </p>
        <p className="text-lg text-gray-700">
          An <strong>else statement</strong> can be optionally added to an if
          statement:
        </p>
        <p className="text-lg text-gray-700">
          If the condition evaluates to <strong>True</strong>, the code inside
          the <code className="font-mono text-blue-500">if</code> block is
          executed. If the condition evaluates to <strong>False</strong>, the
          code inside the <code className="font-mono text-blue-500">else</code>{" "}
          block is executed.
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
          if is_weekend: {"\n"}
          {"    "}print(&quot;Time to relax!&quot;){"\n"}
          else: {"\n"}
          {"    "}print(&quot;Back to work!&quot;)
        </pre>
        <hr className="my-6 border-t border-gray-300" />
        <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
        <p className="text-sm text-gray-700 mt-2">
          Wow, due to the class&apos;s low average score, the teacher just added a
          curve to the test grades!
        </p>
        <p className="text-sm text-gray-700 mt-2">
          Create a <code className="font-mono text-blue-500">pets.py</code>{" "}
          program that checks whether a grade is above or below 55. Start by
          copy-pasting this code:
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
          animal = input(&quot;What&apos;s your favorite animal?&quot;){"\n"}
        </pre>
        <p className="text-sm text-gray-700 mt-2">
          Write an <code className="font-mono text-blue-500">if/else</code>{" "}
          statement to check the following:
        </p>
        <ul className="list-disc list-inside text-md text-gray-700 mt-2">
          <li>
            If animal is cat, print <code>&quot;Woohoo you are a cat person!&quot;</code>
          </li>
          <li>
            Else, print{" "}
            <code>&quot;That&apos;s a very good animal, but it&apos;s not a cat.&quot;</code>
          </li>
        </ul>
        <p className="text-sm text-gray-700 mt-2">
          Run the code, then change the value of{" "}
          <code className="font-mono text-blue-500">animal</code> and rerun it a
          few times to ensure it&apos;s working as intended.
        </p>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#E89E4D",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#FFB26F",
              color: "#54473F",
              minHeight: "150px",
              borderRadius: "10px",
            }}
          />
        </div>{" "}
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "80%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>
          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#859F3D",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#FFFFE0",
                }}
              >
                Output:
              </h2>
              <pre
                style={{
                  fontSize: "1rem",
                  color: "#FFFFE0",
                  marginTop: "10px",
                }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: buttonClicked ? "#FFFFE0" : "#859F3D",
                color: buttonClicked ? "#859F3D" : "#FFFFE0",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
                transition: "all 0.3s ease-in-out",
              }}
            >
              {buttonClicked ? "Retry 🔄" : "Run Code 🎉"}
            </button>{" "}
          </div>{" "}
        </div>
      </div>
    </div>
  );
};

export default AreYouACatPerson;
