import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const BookOfAnswers = () => {
  const [code] = useState("Ask your question: \n");
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [animatedCode, setAnimatedCode] = useState("");
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const [showConfetti, setShow<PERSON>onfetti] = useState(false);
  const [buttonClicked, setButtonClicked] = useState(false);
  const outputRef = useRef(null);

  const typedText = useTypingAnimation("Book of Answers", 100);


  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      setUserInput(lines.slice(1).join("\n"));
    }
  };


  const runCode = () => {
    if (buttonClicked) {

      setUserInput("");
      setOutput("");
      setShowOutput(false);
      setButtonClicked(false);
      setShowConfetti(false);
      return;
    }

    try {

      const simulatedInput = userInput?.trim();


      if (!simulatedInput) {
        setOutput("Error: Please ask a question.");
        setShowOutput(true);
        smoothScrollToOutput();
        return;
      }


      const answers = [
        "Yes - definitely.",
        "It is decidedly so.",
        "Without a doubt.",
        "Reply hazy, try again.",
        "Ask again later.",
        "Better not tell you now.",
        "My sources say no.",
        "Outlook not so good.",
        "Very doubtful.",
      ];


      const randomAnswer = answers[Math.floor(Math.random() * answers.length)];


      const outputMessage = `Question:         ${simulatedInput}\nBook of Answers:  ${randomAnswer}`;


      setOutput(outputMessage);
      setShowOutput(true);
      setShowConfetti(true);


      setTimeout(() => setShowConfetti(false), 1000);


      smoothScrollToOutput();


      setButtonClicked(true);
    } catch (error) {
      console.error(error);
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
    }
  };


  const smoothScrollToOutput = () => {
    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #84a59d, #bdb2ff)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #84a59d, #bdb2ff)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <div className="mt-8 text-center text-gray-800">
          <p className="text-lg mt-4">
            In Python, modules are{" "}
            <code className="font-mono text-blue-500">.py</code> files
            containing Python code that we can import into other programs. The
            Python standard library has over 200 modules for us to use.
          </p>
          <p className="text-lg mt-2">
            One of these modules, called{" "}
            <code className="font-mono text-blue-500">random</code>, lets us
            generate random numbers with its{" "}
            <code className="font-mono text-blue-500">.randint()</code>{" "}
            function.
          </p>
          <p className="text-lg mt-2">
            First, we need to import the module so we can access its functions:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            import random
          </pre>
          <p className="text-lg mt-4">
            Next, we&apos;ll create a variable to store a randomly generated value.
            Declare a variable called{" "}
            <code className="font-mono text-blue-500">num</code>, and assign it
            the function call:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            num = random.randint(1, 9)
          </pre>
          <p className="text-lg mt-4">
            This generates a random number between 1 and 9 (inclusive of both).
          </p>
          <p className="text-lg mt-4">
            So the complete code will look like this:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            import random{"\n\n"}
            num = random.randint(1, 9){"\n\n"}
            print(num)
          </pre>
          <p className="text-lg mt-4">Try running this code! ☝️</p>
          <p className="text-lg mt-4">
            The output should be different each time: 2, 8, 5, 9, 2, 1, 3...
          </p>
          <hr className="my-6 border-t border-gray-300" />
          <p className="mt-6 text-lg font-bold text-black">INSTRUCTIONS</p>
          <p className="text-lg mt-2">
            Have you ever heard of the book with answers to all the things in
            the world?
          </p>
          <p className="text-lg mt-2">
            The book of answers, written by Carol Bolt, is a humorous self-help
            book, with a single line of answer on each page. You hold the closed
            book, ask a question, and then open to any page at random. And boom!
            That&apos;s your answer.
          </p>
          <p className="text-lg mt-4">
            Let&apos;s code our own book of answers. Create a{" "}
            <code className="font-mono text-blue-500">book_of_answers.py</code>{" "}
            that can respond to any Yes or No question with a different answer
            each time it executes.
          </p>
          <p className="text-lg mt-4">
            Add the following answers or make your own answers:
          </p>
          <ul className="list-disc list-inside mt-4">
            <li>Yes - definitely.</li>
            <li>It is decidedly so.</li>
            <li>Without a doubt.</li>
            <li>Reply hazy, try again.</li>
            <li>Ask again later.</li>
            <li>Better not tell you now.</li>
            <li>My sources say no.</li>
            <li>Outlook not so good.</li>
            <li>Very doubtful.</li>
          </ul>
          <p className="text-lg mt-4">Your output should be in this format:</p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            Question: [Question]{"\n"}
            Book of Answers: [Answer]
          </pre>
          <p className="text-lg mt-4">For example:</p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            Question: Am I good at python yet?{"\n"}
            Book of Answers: Without a doubt.
          </pre>
        </div>
        <pre
          style={{
            backgroundColor: "#FFF8E1",
            padding: "15px",
            borderRadius: "10px",
            textAlign: "left",
            color: "#444",
            marginTop: "10px",
            border: "1px solid #FFEE93",
          }}
        >
          {`    # Python program for a Book of Answers
import random

answers = [
    "Yes - definitely.",
    "It is decidedly so.",
    "Without a doubt.",
    "Reply hazy, try again.",
    "Ask again later.",
    "Better not tell you now.",
    "My sources say no.",
    "Outlook not so good.",
    "Very doubtful."
]

question = input("Ask your question: ")
answer = random.choice(answers)

print(f"Question: {question}")
print(f"Book of Answers: {answer}")`}
        </pre>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#0b8a54",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#0ead69",
              color: "#efefd0",
              minHeight: "150px",
              borderRadius: "10px",
              overflow: "auto",
              whiteSpace: "pre-wrap",
            }}
          />
        </div>
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "89%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>
          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#f7ede2",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#524C4F",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#524C4F", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={() => {
                if (buttonClicked) {

                  setButtonClicked(false);
                  setUserInput("");
                  setOutput("");
                  setShowOutput(false);
                  setShowConfetti(false);
                  setAnimatedCode("");
                  setIsAnimationCompleted(false);
                } else {

                  runCode();
                  setButtonClicked(true);
                }
              }}
              className={styles.fadeInUp}
                style={{
                  position: "relative",
                  zIndex: 10,
                  marginTop: "20px",
                  backgroundColor: buttonClicked ? "#524C4F" : "#f7ede2",
                  color: buttonClicked ? "#f7ede2" : "#524C4F",
                  padding: "10px 20px",
                  borderRadius: "30px",
                  fontWeight: "bold",
                  fontSize: "1rem",
                  cursor: "pointer",
                  boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
                  transition: "all 0.3s ease-in-out",
                }}
            >
              {buttonClicked ? "Retry 🔄" : "Run Code 🎉"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookOfAnswers;
