import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const OhYouAreActuallyMakingMeGuess = () => {
  const [code] = useState(
    `Welcome to the Guessing Game!\nGuess the number:  \n`
  );
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);
  const [buttonClicked, setButtonClicked] = useState(false);
  const typedText = useTypingAnimation("YOU'RE MAKING ME GUESS !", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


  const runCode = () => {
    try {
      const simulatedInput = userInput?.trim().toLowerCase();
  
      if (!simulatedInput) {
        setOutput("Error: Please provide an input.");
        setShowOutput(true);
        scrollToOutput();
        return;
      }
  
      let output = "";
      let confetti = false;
  

      const secretNumber = 6;
      let guess = parseInt(simulatedInput);
  

      if (guess === secretNumber) {
        output = "You got it!";
        confetti = true;
        setOutput(output);
        setShowConfetti(confetti);
        setShowOutput(true);
        scrollToOutput();
  

        if (confetti) {
          setTimeout(() => setShowConfetti(false), 1000);
        }
      }
    } catch {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
    }
  };
  

  const scrollToOutput = () => {
    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {

      const lines = newCode.split("\n");
      const prompt = "Guess the number:  ";
      console.log(prompt);
      const inputWithoutPrompt = lines.slice(2).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #e27396, #8f2d56)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right,  #e27396, #8f2d56)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <div className="max-w-2xl text-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">WHILE LOOP</h2>
        <p className="text-lg text-gray-700">
          Now that we got a sneak peek of a <code className="font-mono text-blue-500">while</code> loop, let&apos;s see what it does!
        </p>
        <p className="text-lg text-gray-700 mt-2">
          A <code className="font-mono text-blue-500">while</code> loop looks very similar to an <code className="font-mono text-blue-500">if</code> statement. Just like an <code className="font-mono text-blue-500">if</code> statement, it executes the code if the condition is <strong>True</strong>.
        </p>
        <p className="text-lg text-gray-700 mt-2">
          However, the difference is that the <code className="font-mono text-blue-500">while</code> loop will continue to execute the code inside of it, over and over again, as long as the condition is <strong>True</strong>.
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
          {"while condition:\n\t#code\n"}
        </pre>
        <p className="text-lg text-gray-700 mt-2">
          In other words, instead of executing once <em>if</em> a condition is true, it executes again and again <em>while</em> that condition is true.
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
          {"guess = 0\n\nwhile guess != 6:\n\tguess = int(input('Guess the number: '))\n"}
        </pre>
        <p className="text-lg text-gray-700 mt-2">
          This will run over and over again until the user guesses the number 6:
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
          {"Guess the number: 5\nGuess the number: 3\nGuess the number: 6\n"}
        </pre>
        <p className="text-lg text-gray-700 mt-2">
          The variable <code className="font-mono text-blue-500">guess</code> starts off at 0 on the first line, and then the program dives into the while loop:
        </p>
        <ul className="list-disc text-center text-gray-700 mb-3 pl-6 mt-2 bg-gray-100 rounded-lg">
          <li className="p-2">It checks the condition: is it true that 0 isn&apos;t equal to 6? Yep. So, the code inside runs.</li>
          <li className="p-2">Then, it checks again: is it true that 5 isn&apos;t equal to 6? Still true, so the code inside runs again.</li>
          <li className="p-2">It checks once more: is it true that 3 isn&apos;t equal to 6? Yep, so the code keeps running.</li>
          <li className="p-2">Finally, it checks: is it true that 6 isn&apos;t equal to 6? Nope! The condition is false, so the loop exits and skips the code inside.</li>
        </ul>
        <p className="text-lg text-gray-700 mt-2">
          To sum it up (pun totally intended!), the condition is checked at the start of each loop. The moment the condition turns false, the program exits the while loop and continues with the next line of code.
        </p>
        <p className="text-lg text-gray-700 mt-2">
          <strong>Note:</strong> If the condition is false right from the start, the code block inside the loop won&apos;t run at all—it&apos;ll just be skipped entirely.
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
        <p className="text-sm text-gray-700 mt-2">
          Let&apos;s continue on from the code above. Create a <code className="font-mono text-blue-500">guess.py</code> program and type in the following:
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
          {"guess = 0\n\nwhile guess != 6:\n\tguess = int(input('Guess the number:  '))\n\nprint('You got it!')\n"}
        </pre>
        <p className="text-sm text-gray-700 mt-2">
          Run the code a few times so that you understand what it does.
        </p>
        <p className="text-sm text-gray-700 mt-2">
          Let&apos;s make it so that it&apos;s the same guessing game, but there is a new limit to the number of tries (it was infinite before).
        </p>
        <p className="text-sm text-gray-700 mt-2">
          First, introduce a variable called <code className="font-mono text-blue-500">tries</code> at the top and give it a value of 0.
        </p>
        <p className="text-sm text-gray-700 mt-2">
          Then, add the <code className="font-mono text-blue-500">tries</code> variable to the <code className="font-mono text-blue-500">while</code> loop using a relational operator.
        </p>
        </div>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#04B98D",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#06d6a0",
              color: "#6d2e46",
              minHeight: "150px",
              borderRadius: "10px",
            }}
          />
        </div>{" "}
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "77%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#003d5b",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#edeec9",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#edeec9", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={() => {
                if (buttonClicked) {

                  setButtonClicked(false);
                  setUserInput("");
                  setOutput("");
                  setShowOutput(false);
                  setShowConfetti(false);
                  setAnimatedCode("");
                  setIsAnimationCompleted(true);
                } else {

                  runCode();
                  setButtonClicked(true);
                }
              }}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: buttonClicked ? "#edeec9" : "#003d5b",
                color: buttonClicked ? "#003d5b" : "#edeec9",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
                transition: "all 0.3s ease-in-out",
              }}
            >
              {buttonClicked ? "Retry 🔄" : "Run Code 🎉"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OhYouAreActuallyMakingMeGuess;
