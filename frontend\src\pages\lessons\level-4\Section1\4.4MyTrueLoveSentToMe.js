import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const MyTrueLoveSentToMe = () => {
  const [code, setCode] = useState(`# The Twelve Days of Christmas

# List of days in the song
days = [
    "first", "second", "third", "fourth", "fifth", 
    "sixth", "seventh", "eighth", "ninth", "tenth", 
    "eleventh", "twelfth"
]

# List of gifts for each day
gifts = [
    "a partridge in a pear tree",
    "two turtle doves",
    "three French hens",
    "four calling birds",
    "five golden rings",
    "six geese a-laying",
    "seven swans a-swimming",
    "eight maids a-milking",
    "nine ladies dancing",
    "ten lords a-leaping",
    "eleven pipers piping",
    "twelve drummers drumming"
]

# Loop to iterate through each day and print the gifts
for i in range(12):
    print(f"On the {days[i]} day of Christmas, my true love gave to me:")
    for j in range(i, -1, -1): # Print gifts in reverse order
        print(gifts[j])
  `);

  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);

  const outputRef = useRef(null);

  const typedText = useTypingAnimation("My True Love Sent to Me", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);

  const runCode = () => {
    try {
      const expectedCode = `
# The Twelve Days of Christmas

# List of days in the song
days = [
    "first", "second", "third", "fourth", "fifth", 
    "sixth", "seventh", "eighth", "ninth", "tenth", 
    "eleventh", "twelfth"
]

# List of gifts for each day
gifts = [
    "a partridge in a pear tree",
    "two turtle doves",
    "three French hens",
    "four calling birds",
    "five golden rings",
    "six geese a-laying",
    "seven swans a-swimming",
    "eight maids a-milking",
    "nine ladies dancing",
    "ten lords a-leaping",
    "eleven pipers piping",
    "twelve drummers drumming"
]

# Loop to iterate through each day and print the gifts
for i in range(12):
    print(f"On the {days[i]} day of Christmas, my true love gave to me:")
    for j in range(i, -1, -1): # Print gifts in reverse order
        print(gifts[j])
      `.trim();

      if (code.trim() === expectedCode) {
        const outputLines = [];
        const days = [
          "first",
          "second",
          "third",
          "fourth",
          "fifth",
          "sixth",
          "seventh",
          "eighth",
          "ninth",
          "tenth",
          "eleventh",
          "twelfth",
        ];
        const gifts = [
          "a partridge in a pear tree",
          "two turtle doves",
          "three French hens",
          "four calling birds",
          "five golden rings",
          "six geese a-laying",
          "seven swans a-swimming",
          "eight maids a-milking",
          "nine ladies dancing",
          "ten lords a-leaping",
          "eleven pipers piping",
          "twelve drummers drumming",
        ];
        for (let i = 0; i < 12; i++) {
          outputLines.push(
            `On the ${days[i]} day of Christmas, my true love gave to me:`
          );
          for (let j = i; j >= 0; j--) {
            outputLines.push(gifts[j]);
          }
          outputLines.push("");
        }
        setOutput(outputLines.join("\n"));
        setShowConfetti(true);
      } else {
        setOutput("Error: Please Correct The Code!");
        setShowConfetti(false);
      }

      setShowOutput(true);

      setTimeout(() => setShowConfetti(false), 1000);

      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
    } catch (error) {
      console.error(error);
      setOutput("Error: Incorrect Code! Please try again.");
      setShowOutput(true);
    }
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      setCode(newCode);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #FFDFDE, #6A7BA2)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #FFDFDE, #6A7BA2)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <div className="max-w-2xl text-center">
          <p className="text-lg text-gray-700">
            Remember the Christmas classic,{" "}
            <strong>&quot; The Twelve Days of Christmas&quot; </strong>? Let&apos;s recreate it
            using Python programming! Through this activity, you&apos;ll learn how to
            use lists and loops to build dynamic outputs.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            In programming, loops are a fundamental tool to automate repetitive
            tasks. In this case, we will use a{" "}
            <code className="font-mono text-blue-500">for</code> loop to
            generate the song&apos;s lyrics efficiently.
          </p>

          {}
          <hr className="my-6 border-t border-gray-300" />
          <h2 className="text-2xl font-bold text-gray-800 mt-6">
            STRING INTERPOLATION
          </h2>
          <p className="text-lg text-gray-700 mt-2">
            Before we dive into the next instructions, let&apos;s explore a new
            concept that&apos;s closely related to string concatenation.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            String interpolation allows us to insert the values of variables
            directly into placeholders within a string.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            For example, if you have a greeting template like{" "}
            <code className="font-mono text-blue-500">
              &apos;Hi {"{name}"}, nice to meet you!&apos;
            </code>
            , and you want to insert an actual name where{" "}
            <code className="font-mono text-blue-500">{"{name}"}</code> is, that&apos;s
            string interpolation.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            You can achieve this by using the{" "}
            <code className="font-mono text-blue-500">{`{}`}</code> sign to
            represent placeholders that will be filled with variable values.
          </p>

          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
            {`# String interpolation

for i in range(5):
  print(f'The square of {i} is {i*i}')
`}
          </pre>
          <p className="text-lg text-gray-700 mt-2">
            Notice the <code className="font-mono text-blue-500">f</code> prefix
            before the quotes, which tells Python to interpret the placeholders
            inside the string.
          </p>

          <hr className="my-6 border-t border-gray-300" />

          {}
          <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
          <p className="text-sm text-gray-700 mt-2">
            Remember the old Christmas song{" "}
            <a
              href="https://genius.com/Christmas-songs-the-twelve-days-of-christmas-lyrics"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500"
            >
              The Twelve Days of Christmas
            </a>{" "}
            that we used to sing as kids? Let&apos;s recreate it using code.
          </p>
          <p className="text-sm text-gray-700 mt-2">
            Create a{" "}
            <code className="font-mono text-blue-500">christmas.py</code>{" "}
            program and use a{" "}
            <code className="font-mono text-blue-500">for</code> loop and a{" "}
            <code className="font-mono text-blue-500">range()</code> function to
            print out all the verses of “The Twelve Days of Christmas”.
          </p>
          <p className="text-sm text-gray-700 mt-2">
            Copy and paste this data:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
            {`# Twelve Days of Christmas
days = [
    "first", "second", "third", "fourth", "fifth",
    "sixth", "seventh", "eighth", "ninth", "tenth",
    "eleventh", "twelfth"
]

gifts = [
    "a partridge in a pear tree",
    "two turtle doves",
    "three French hens",
    "four calling birds",
    "five golden rings",
    "six geese a-laying",
    "seven swans a-swimming",
    "eight maids a-milking",
    "nine ladies dancing",
    "ten lords a-leaping",
    "eleven pipers piping",
    "twelve drummers drumming"
]
`}
          </pre>
          <p className="text-sm text-gray-700 mt-2">
            And, of course, you should use string interpolation!
          </p>
        </div>{" "}
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#FFB8B6",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#FFDFDE",
              color: "#1E4174",
              minHeight: "670px",
              borderRadius: "10px",
            }}
          />
        </div>
        <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#A2A2A1",
                color: "#195190",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "47%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#A2A2A1",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#195190",
                }}
              >
                Output:
              </h2>
              <pre
                style={{
                  fontSize: "1rem",
                  color: "#195190",
                  marginTop: "10px",
                }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MyTrueLoveSentToMe;
