import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const GoodOlFizzBuzz = () => {
  const [code, setCode] = useState(`
for (let i = 1; i <= 100; i++) {
  if (i % 3 === 0 && i % 5 === 0) {
    console.log('FizzBuzz');
  } else if (i % 3 === 0) {
    console.log('Fizz');
  } else if (i % 5 === 0) {
    console.log('Buzz');
  } else {
    console.log(i);
  }
}
  `);
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);

  const outputRef = useRef(null);

  const typedText = useTypingAnimation("GOOD OL' FIZZBUZZ", 100);

  const [animatedCode, setAnimatedCode] = useState("");
  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);

  const runCode = () => {
    try {
      const expectedCode = `

for (let i = 1; i <= 100; i++) {
  if (i % 3 === 0 && i % 5 === 0) {
    console.log('FizzBuzz');
  } else if (i % 3 === 0) {
    console.log('Fizz');
  } else if (i % 5 === 0) {
    console.log('Buzz');
  } else {
    console.log(i);
  }
}
      `.trim();

      if (code.trim() === expectedCode) {
        const fizzBuzzOutput = Array.from({ length: 100 }, (_, i) => {
          const num = i + 1;
          if (num % 3 === 0 && num % 5 === 0) return "FizzBuzz";
          if (num % 3 === 0) return "Fizz";
          if (num % 5 === 0) return "Buzz";
          return num;
        }).join("\n");

        setOutput(fizzBuzzOutput);
        setShowOutput(true);
        setShowConfetti(true);
      } else {
        setOutput("Error: Please Correct The Code!");
        setShowOutput(true);
        setShowConfetti(false);
      }

      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);

      setTimeout(() => {
        document
          .querySelector(`.${styles.confetti}`)
          ?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
    } catch (error) {
      console.error(error);
      setOutput("Error: Incorrect Code! Please try again.");
      setShowOutput(true);
    }
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      setCode(newCode);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #1a7a4c, #101820)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right,  #1a7a4c, #101820)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <div className="max-w-2xl text-center">
          <div className="mt-4 mb-4 flex justify-center items-center">
            <p className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 animate-pulse">
              Congrats! <span className="text-yellow-500">🎉</span>
            </p>
          </div>

          <p className="text-lg text-gray-700">
            Congrats! You&apos;ve made it to the end of this chapter! Let&apos;s do a
            recap. 🥳<br/>
            The <code>while</code> loop iterates over and over again while the
            condition is true:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            {`while coffee < 1:
  print('tired')`}
          </pre>
          <p className="text-lg text-gray-700 mt-4">
            The <code>for</code> loop and the <code>range()</code> function are
            used to iterate a specific number of times:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            {`for i in range(10):
  print(i)`}
          </pre>
          <p className="text-lg text-gray-700 mt-6">
            Now let&apos;s take your learnings to the test!
          </p>
          <hr className="my-6 border-t border-gray-300" />
          <p className="text-lg font-bold text-black mt-4">INSTRUCTIONS</p>
          <p className="text-sm text-gray-700 mt-2">
            Fizz Buzz is a children&apos;s word game that teaches division. It&apos;s also
            a classic technical interview question at countless companies. 🐝
            Though this challenge may appear simple to experienced coders, it is
            designed to weed out 90% of job candidates who cannot apply their
            coding knowledge to a new problem creatively. Want to give it a try?
          </p>
          <p className="text-lg text-gray-700 mt-4">
            Create a <code>fizz_buzz.py</code> program that outputs numbers from
            1 to 100.
          </p>
          <p className="text-lg text-gray-700 mt-4">Here&apos;s the catch:</p>
          <ul className="text-lg text-gray-700 mt-2 list-disc list-inside">
            <li>For multiples of 3, print &quot;Fizz&quot; instead of the number.</li>
            <li>For multiples of 5, print &quot;Buzz&quot; instead of the number.</li>
            <li>For multiples of both 3 and 5, print &quot;FizzBuzz&quot;.</li>
          </ul>
          <p className="text-lg text-gray-700 mt-4">
            Your output should look something like this:
          </p>
          <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
            {`1
2
Fizz
4
Buzz
Fizz
7
8
Fizz
Buzz
11
Fizz
13
14
FizzBuzz`}
          </pre>
        </div>{" "}
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#B8946E",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#D4B996",
              color: "#101820",
              minHeight: "300px",
              borderRadius: "10px",
            }}
          />
        </div>
        <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#F5C7B8",
                color: "#3C1053",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "39%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#F5C7B8",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#3C1053",
                }}
              >
                Output:
              </h2>
              <pre
                style={{
                  fontSize: "1rem",
                  color: "#3C1053",
                  marginTop: "10px",
                }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GoodOlFizzBuzz;
