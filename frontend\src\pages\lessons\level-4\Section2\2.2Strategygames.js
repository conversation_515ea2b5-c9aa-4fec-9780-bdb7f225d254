import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Strategygames = () => {
  const typedText = useTypingAnimation("Strategy Games", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          Strategy games are all about using your brainpower to outwit your
          opponents. These games demand careful planning, quick thinking, and a
          strong grasp of the game&apos;s rules and mechanics.
        </p>
        <img src="/starcraft.png" alt="StarCraft" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>StarCraft:</strong> In StarCraft, you command an army of space
          marines, aliens, or robots, aiming to outmaneuver your opponent by
          building bases, gathering resources, and leading your troops into
          battle. It&apos;s like a high-tech chess game where every decision matters.
          StarCraft is a legend in esports, known for its deep strategy and
          intense matches.
        </p>
        <img src="/clash-royale.png" alt="Clash Royale" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Clash Royale:</strong> This game blends card games with tower
          defense. You collect and upgrade cards representing characters and
          spells, using them to destroy your opponent&apos;s towers while defending
          your own. It&apos;s fast-paced, fun, and packed with strategic choices—do
          you attack now or save your best cards for later?
        </p>
        <img src="/chess.png" alt="Chess" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Chess:</strong> Yes, even the classic game of chess has its
          place in esports! Online chess tournaments have skyrocketed in
          popularity, with players from across the globe competing in real-time
          matches. Chess is all about thinking ahead and predicting your
          opponent&apos;s moves, making it one of the most strategic games ever.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Strategy games are perfect for those who love solving puzzles,
          thinking ahead, and crafting clever plans. These games sharpen your
          mind and test your ability to stay calm under pressure!
        </p>
      </div>
    </div>
  );
};

export default Strategygames;
