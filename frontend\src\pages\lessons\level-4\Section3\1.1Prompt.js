import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("WHAT ? WHY ? AND HOW ?", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>What is a Prompt ?</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Imagine a prompt as a magical incantation that brings the AI to life. When
          you write a prompt, you&apos;re casting a spell, giving the AI specific
          instructions on what you want it to do. It&apos;s like asking a question or
          issuing a command.
        </p>
        <p className="text-lg text-gray-700 mb-4">For example:</p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Prompt:</strong> &quot;Tell me a story about a fearless knight.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>AI Response:</strong> &quot;Once upon a time, in a distant kingdom,
          there was a fearless knight named Sir Lancelot...&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Prompts can range from being simple to quite complex, depending on what you
          want the AI to accomplish. The more refined your prompt, the more accurate
          and engaging the AI&apos;s response will be.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Why Prompts Matter</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Prompts are crucial because they steer the AI&apos;s responses. Just like how
          clear directions help you find your way, a well-crafted prompt guides the AI
          to deliver the best answer or create something extraordinary.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>For example:</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Vague Prompt:</strong> &quot;Tell me about animals.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Specific Prompt:</strong> &quot;Explain the behavior of nocturnal
          animals in the rainforest.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          The second prompt gives the AI more precise instructions, leading to a more
          relevant and useful response.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Examples of Simple Prompts</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>&quot;What is the capital of Japan?&quot;</li>
          <li>&quot;Write a short poem about the night sky.&quot;</li>
          <li>&quot;Share some interesting facts about dolphins.&quot;</li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          These prompts are easy for the AI to understand. As you become more
          skilled at writing prompts, you can explore more intricate ones!
        </p>
      </div>
    </div>
  );
};

export default Prompt;
