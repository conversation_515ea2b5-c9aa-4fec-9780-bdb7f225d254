import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("WHERE WOULD I EVER USE THIS", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Prompts in Customer Service</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          AI is frequently used in customer service to answer queries and solve
          problems. The quality of service depends largely on how well the
          prompts are written. A clear and specific prompt ensures the AI
          provides accurate information.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompt:</strong> &quot;How do I reset my password on your
          website?&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
          <br />
          Imagine you work for a tech company. Write prompts that an AI could
          use to assist customers with common issues, like setting up a new
          account or troubleshooting a device.
        </p>
        <hr className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Using Prompts in Creative Writing</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Writers often use prompts to spark ideas or overcome writer&apos;s block.
          AI can generate story ideas, character profiles, or even entire plots
          based on your prompts.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompt:</strong> &quot;Write a short story about a time
          traveler who visits ancient Rome.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
          <br />
          Use AI to help you start a story. Write a prompt that sets up the
          beginning of a tale, and see how the AI continues it. You can then
          build on the AI&apos;s ideas to craft your own unique story.
        </p>
        <hr className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>AI in Games: How Prompts Enhance Gameplay</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          In some games, AI uses prompts to generate dynamic content, like
          levels, quests, or character interactions. Players can even use
          prompts to influence the game&apos;s storyline.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompt:</strong> &quot;Create a quest where the hero must
          save a hidden village from a dragon.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
          <br />
          Design a simple game scenario using AI prompts. Write prompts for
          characters, challenges, and rewards that would make the game engaging.
        </p>
        <hr className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Future of Prompt Engineering: What&apos;s Next?</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Prompt engineering is an evolving field, with new applications being
          discovered regularly. From education to entertainment, the
          possibilities are endless.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
          <br />
          Research a new or emerging AI application that uses prompts. Share
          your findings with the class and discuss how prompt engineering might
          shape the future.
        </p>
      </div>
    </div>
  );
};

export default Prompt;
