import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation(
    "Collector Myself",
    100
  );

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Gathering Your Favorite Prompts</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          As you&apos;ve been working on crafting prompts, you&apos;ve likely come up with
          some that you&apos;re particularly proud of. Now, it&apos;s time to gather these
          gems into a collection. This prompt booklet will be your go-to
          resource, providing inspiration and showcasing your prompt-writing
          skills whenever you need it.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Review the prompts you&apos;ve written so far and select your top 10.
          Organize them neatly, and consider adding a brief note next to each
          one explaining what you like about it or why it works well.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Tips for Writing Great Prompts</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Compiling your prompts is also a chance to reflect on what makes a
          prompt effective. Here are some key tips to remember and include in
          your booklet:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Be Specific:</strong> Details help the AI respond more
            accurately.
          </li>
          <li>
            <strong>Be Clear:</strong> Ensure your prompt is easy to understand.
          </li>
          <li>
            <strong>Be Creative:</strong> Don’t hesitate to explore unusual
            ideas.
          </li>
          <li>
            <strong>Experiment:</strong> Try different wordings to see how
            responses vary.
          </li>
          <li>
            <strong>Learn from Feedback:</strong> If a prompt doesn’t work as
            planned, think about how you can refine it.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Add a short section in your booklet with these tips or any others
          you&apos;ve discovered. You can even include examples of before-and-after
          prompts to illustrate how you&apos;ve improved.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Adding Illustrations and Examples</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Your prompt booklet doesn’t have to be all text. Make it more engaging
          by adding illustrations, screenshots of AI responses, or examples
          showing how different prompts led to different outcomes.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Incorporate visual elements into your booklet. Draw pictures, create
          diagrams, or take screenshots of interesting AI responses. This will
          make your booklet more enjoyable to browse and help others understand
          the impact of well-crafted prompts.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Sharing Your Prompts with Others</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          A great way to enhance your prompt-writing skills is by sharing your
          work with others. You can gain valuable feedback, exchange ideas, and
          even collaborate on new prompts.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Organize a session with your classmates or friends where you share
          your booklets. Show them your prompts and ask for their feedback. You
          can also review their prompts and offer suggestions. This
          collaborative effort is a fantastic way to learn from each other and
          generate new ideas.
        </p>
      </div>
    </div>
  );
};

export default Prompt;
