import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("CALL ME MASTER", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Multi-Step Prompts: Crafting Complex Queries</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Sometimes, a single prompt isn&apos;t enough to get the full information or
          results you&apos;re seeking. In these cases, multi-step prompts are useful.
          These involve giving the AI a series of prompts, each one building on
          the last.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example:</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Step 1:</strong> &quot;Explain the process of photosynthesis.&quot;
          </li>
          <li>
            <strong>Step 2:</strong> &quot;Now, describe why this process is crucial
            for the food chain.&quot;
          </li>
          <li>
            <strong>Step 3:</strong> &quot;Finally, connect this process to human
            impacts on the environment.&quot;
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          Each step adds more detail, helping the AI navigate a complex topic.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Write a multi-step prompt about a topic you find interesting. Start
          with a basic question, then add follow-up prompts to deepen the
          discussion. Observe how each step adds layers to the AI&apos;s response.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Using Prompts for Research</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          AI can be a powerful tool for research, helping you quickly find
          detailed answers, summaries, or comparisons. By crafting effective
          prompts, you can guide the AI to provide you with in-depth
          information.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompt:</strong> &quot;Compare and contrast the causes of
          World War I and World War II.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Pick a research topic for a school project or something that interests
          you. Write a series of prompts to help you gather information on that
          topic. Use the AI&apos;s responses to create an outline or summary for your
          research.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Combining Prompts for Enhanced Results</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          At times, blending different types of prompts can yield more
          interesting or useful results. You might begin with a creative prompt
          to generate ideas, then use a more specific prompt to explore one of
          those ideas in depth.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example:</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Creative Prompt:</strong> &quot;Imagine a futuristic city where
            everyone uses flying cars. What challenges might people face?&quot;
          </li>
          <li>
            <strong>Specific Prompt:</strong> &quot;Explain how air traffic control
            would operate in a city with flying cars.&quot;
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Experiment with combining prompts. Start with a broad, creative
          prompt, and then narrow the focus with more specific questions. Notice
          how the AI&apos;s responses evolve as you guide it through the topic.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Reflecting on Feedback: Refining Your Prompts</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          One of the best ways to improve your prompt-writing skills is to
          reflect on feedback. If a prompt didn&apos;t work as expected, think about
          why. Did it need more details? Was it too vague? Use this feedback to
          refine and improve your prompts.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Review some of your earlier prompts and think about how they could be
          improved. Rewrite them based on what you&apos;ve learned, and test the new
          versions with the AI. Compare the results to see how your skills have
          developed.
        </p>
      </div>
    </div>
  );
};

export default Prompt;
