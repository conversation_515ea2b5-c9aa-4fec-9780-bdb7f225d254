import React, { useState } from "react";
import useTypingAnimation from "../Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const questions = [
  {
    question: "Who created Python and when?",
    options: [
      { text: "<PERSON>, 1991", isCorrect: true },
      { text: "<PERSON>, 1989", isCorrect: false },
      { text: "<PERSON>, 2004", isCorrect: false },
      { text: "<PERSON>, 1972", isCorrect: false },
    ],
  },
  {
    question: "What is the correct syntax to print a message in Python?",
    options: [
      { text: 'echo "Hello World"', isCorrect: false },
      { text: 'print("Hello World")', isCorrect: true },
      { text: 'printf("Hello World")', isCorrect: false },
      { text: "print Hello World", isCorrect: false },
    ],
  },
  {
    question: "What value does a Boolean type represent?",
    options: [
      { text: "Text", isCorrect: false },
      { text: "Whole numbers", isCorrect: false },
      { text: "True or False", isCorrect: true },
      { text: "Decimals", isCorrect: false },
    ],
  },
  {
    question:
      "What will the following code output if the user enters 'mypassword' as the input?\n\npassword = input('Enter a password: ')\nif len(password) >= 8:\n    print('Strong password')\nelse:\n    print('Weak password')",
    options: [
      { text: "Strong password", isCorrect: true },
      { text: "Weak password", isCorrect: false },
    ],
  },
  {
    question:
      "What will the following code output?\n\nimport random\nnum = random.randint(1, 9)\nprint(num)",
    options: [
      { text: "1", isCorrect: false },
      { text: "9", isCorrect: false },
      { text: "A random number between 1 and 9", isCorrect: true },
      { text: "Error", isCorrect: false },
    ],
  },
  {
    question: "Which keyword is used to import a module in Python?",
    options: [
      { text: "module", isCorrect: false },
      { text: "include", isCorrect: false },
      { text: "import", isCorrect: true },
      { text: "require", isCorrect: false },
    ],
  },
  {
    question:
      "Which of the following code snippets will correctly print the numbers from 1 to 5 using a for loop and the range() function, while also demonstrating the concept of string interpolation?",
    options: [
      {
        text: 'for i in range(1, 5):\n    print(f"Number: {i}")',
        isCorrect: false,
      },
      {
        text: 'for i in range(1, 6):\n    print(f"Number: {i}")',
        isCorrect: true,
      },
      {
        text: 'for i in range(1, 6):\n    print("Number: i")',
        isCorrect: false,
      },
      {
        text: 'for i in range(1, 6):\n    print(f"Number: " + str(i))',
        isCorrect: false,
      },
    ],
  },
  {
    question:
      "What will be the result of the following code snippet that utilizes both relational and logical operators?\n\nage = 16\nhas_ticket = False\nif age >= 14 and has_ticket:\n    print('Welcome aboard The Thunderbolt!')\nelif has_ticket:\n    print('Sorry, you must be at least 14 years old to ride.')\nelif age >= 14:\n    print('You need a ticket to ride The Thunderbolt.')\nelse:\n    print('You don't meet the requirements to ride.')",
    options: [
      { text: "Welcome aboard The Thunderbolt!", isCorrect: false },
      {
        text: "Sorry, you must be at least 14 years old to ride.",
        isCorrect: false,
      },
      { text: "You need a ticket to ride The Thunderbolt.", isCorrect: true },
      { text: "You don't meet the requirements to ride.", isCorrect: false },
    ],
  },
  {
    question:
      "Which of the following loops is used when the number of iterations is known in advance?",
    options: [
      { text: "while loop", isCorrect: false },
      { text: "for loop", isCorrect: true },
      { text: "if loop", isCorrect: false },
      { text: "elif loop", isCorrect: false },
    ],
  },
  {
    question: "What does the random.randint(1, 9) function do?",
    options: [
      {
        text: "Generates a random number between 1 and 9, inclusive",
        isCorrect: true,
      },
      { text: "Generates a random number between 0 and 9", isCorrect: false },
      { text: "Generates a random float between 1 and 9", isCorrect: false },
      { text: "None of the above", isCorrect: false },
    ],
  },
  {
    question: "What does the term 'esports' stand for?",
    options: [
      { text: "Extreme Sports", isCorrect: false },
      { text: "Electronic Sports", isCorrect: true },
      { text: "Enhanced Sports", isCorrect: false },
      { text: "Elite Sports", isCorrect: false },
    ],
  },
  {
    question:
      "What was the first known video game competition, and when did it take place?",
    options: [
      { text: "Pong Tournament, 1975", isCorrect: false },
      { text: "Spacewar! Competition, 1972", isCorrect: true },
      { text: "Pac-Man Championship, 1980", isCorrect: false },
      { text: "Mario Bros. Contest, 1985", isCorrect: false },
    ],
  },
  {
    question:
      "Which of the following is a key difference between traditional sports and esports?",
    options: [
      {
        text: "Traditional sports are played indoors, while esports are always outdoors.",
        isCorrect: false,
      },
      {
        text: "Traditional sports require physical activity, whereas esports focus on mental skills.",
        isCorrect: true,
      },
      {
        text: "Traditional sports do not require teamwork, unlike esports.",
        isCorrect: false,
      },
      {
        text: "Esports games have no rules, unlike traditional sports.",
        isCorrect: false,
      },
    ],
  },
  {
    question:
      "Which of these is a popular esports game where players control rocket-powered cars?",
    options: [
      { text: "Fortnite", isCorrect: false },
      { text: "League of Legends", isCorrect: false },
      { text: "Rocket League", isCorrect: true },
      { text: "Super Smash Bros.", isCorrect: false },
    ],
  },
  {
    question:
      "Which of the following games is a battle royale game where players compete to be the last one standing?",
    options: [
      { text: "Overwatch", isCorrect: false },
      { text: "Fortnite", isCorrect: true },
      { text: "FIFA", isCorrect: false },
      { text: "NBA 2K", isCorrect: false },
    ],
  },
  {
    question:
      "What type of game is StarCraft, known for its deep strategy and intense matches?",
    options: [
      { text: "Puzzle Game", isCorrect: false },
      { text: "Battle Game", isCorrect: false },
      { text: "Sports Game", isCorrect: false },
      { text: "Strategy Game", isCorrect: true },
    ],
  },
  {
    question:
      "In which esports game do players choose from heroes with special abilities and work together to complete objectives?",
    options: [
      { text: "Rocket League", isCorrect: false },
      { text: "League of Legends", isCorrect: false },
      { text: "Overwatch", isCorrect: true },
      { text: "Chess", isCorrect: false },
    ],
  },
  {
    question:
      "Which game is a classic puzzle game where players arrange falling blocks to create complete lines?",
    options: [
      { text: "Tetris", isCorrect: true },
      { text: "Mario Party", isCorrect: false },
      { text: "Puyo Puyo", isCorrect: false },
      { text: "Fall Guys", isCorrect: false },
    ],
  },
  {
    question:
      "What is essential for success in team-based esports games, according to the text?",
    options: [
      { text: "Solo Skills", isCorrect: false },
      { text: "Random Strategies", isCorrect: false },
      { text: "Teamwork and Communication", isCorrect: true },
      { text: "Luck and Chance", isCorrect: false },
    ],
  },
  {
    question: "In the context of esports, what does 'knowing your role' mean?",
    options: [
      { text: "Memorizing game rules", isCorrect: false },
      {
        text: "Understanding and performing a specific function within a team",
        isCorrect: true,
      },
      { text: "Mastering every character in the game", isCorrect: false },
      { text: "Playing only one game mode", isCorrect: false },
    ],
  },
  {
    question: "What is a prompt in the context of AI?",
    options: [
      { text: "a) A type of AI technology", isCorrect: false },
      { text: "b) A magical spell", isCorrect: false },
      {
        text: "c) A command or question given to an AI to generate a response",
        isCorrect: true,
      },
      { text: "d) A computer program", isCorrect: false },
    ],
  },
  {
    question: "Why is it important to be specific when writing a prompt?",
    options: [
      { text: "a) It makes the AI work harder", isCorrect: false },
      {
        text: "b) It ensures the AI provides a detailed and relevant response",
        isCorrect: true,
      },
      { text: "c) It confuses the AI", isCorrect: false },
      { text: "d) It doesn't make any difference", isCorrect: false },
    ],
  },
  {
    question: "Which of the following is an example of a clear prompt?",
    options: [
      { text: "a) 'Tell me about animals.'", isCorrect: false },
      { text: "b) 'Explain programming.'", isCorrect: false },
      {
        text: "c) 'Describe the process of creating a landscape painting using watercolors.'",
        isCorrect: true,
      },
      { text: "d) 'What is AI?'", isCorrect: false },
    ],
  },
  {
    question: "What might happen if a prompt is too vague?",
    options: [
      { text: "a) The AI gives a very detailed response", isCorrect: false },
      {
        text: "b) The AI generates an incorrect or irrelevant response",
        isCorrect: true,
      },
      { text: "c) The AI refuses to answer", isCorrect: false },
      {
        text: "d) The AI becomes confused and stops working",
        isCorrect: false,
      },
    ],
  },
  {
    question: "Which of the following best describes a multi-step prompt?",
    options: [
      { text: "a) A single question that is very complex", isCorrect: false },
      {
        text: "b) A series of prompts that guide the AI through a topic step by step",
        isCorrect: true,
      },
      { text: "c) A simple command given to the AI", isCorrect: false },
      {
        text: "d) A repeated question to get the same response",
        isCorrect: false,
      },
    ],
  },
  {
    question: "How does creativity help in writing prompts?",
    options: [
      { text: "a) It makes the prompt longer", isCorrect: false },
      {
        text: "b) It allows you to explore unusual ideas and get more interesting AI responses",
        isCorrect: true,
      },
      { text: "c) It confuses the AI", isCorrect: false },
      {
        text: "d) It makes the prompt easier for the AI to ignore",
        isCorrect: false,
      },
    ],
  },
  {
    question:
      "What is the purpose of gathering your favorite prompts into a collection?",
    options: [
      { text: "a) To memorize them", isCorrect: false },
      {
        text: "b) To showcase your skills and get inspiration for future prompts",
        isCorrect: true,
      },
      { text: "c) To delete them later", isCorrect: false },
      { text: "d) To confuse other users", isCorrect: false },
    ],
  },
  {
    question:
      "Which term describes the quality of being easy to understand in a prompt?",
    options: [
      { text: "a) Specificity", isCorrect: false },
      { text: "b) Complexity", isCorrect: false },
      { text: "c) Clarity", isCorrect: true },
      { text: "d) Ambiguity", isCorrect: false },
    ],
  },
  {
    question: "How can feedback help in improving your prompt-writing skills?",
    options: [
      {
        text: "a) By making you more confident in your original ideas",
        isCorrect: false,
      },
      {
        text: "b) By giving you new perspectives and helping refine your prompts",
        isCorrect: true,
      },
      { text: "c) By making the AI respond faster", isCorrect: false },
      {
        text: "d) By showing you which prompts are too simple",
        isCorrect: false,
      },
    ],
  },
  {
    question: "What is the main goal of prompt engineering?",
    options: [
      { text: "a) To confuse the AI", isCorrect: false },
      { text: "b) To get random responses from the AI", isCorrect: false },
      {
        text: "c) To craft effective prompts that elicit useful and relevant AI responses",
        isCorrect: true,
      },
      { text: "d) To test the limits of AI's knowledge", isCorrect: false },
    ],
  },
];

export default function QuizApp() {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [showFeedback, setShowFeedback] = useState(false);

  const typedText = useTypingAnimation("It's Quiz Time! Let's Have Fun!", 100);

  const handleOptionClick = (option, index) => {
    setSelectedOption(index);
    setShowFeedback(true);

    const isLastQuestion = currentQuestion === questions.length - 1;

    // Update score based on the answer
    if (option.isCorrect) {
      if (isLastQuestion) {
        setScore((prevScore) => prevScore + 4); // Last question, 4 points
      } else {
        setScore((prevScore) => prevScore + 3); // Other questions, 3 points
      }
    } else {
      setScore((prevScore) => Math.max(0, prevScore - 0)); // No penalty
    }

    // Move to the next question after 2 seconds
    setTimeout(() => {
      setSelectedOption(null);
      setShowFeedback(false);
      setCurrentQuestion((prev) => prev + 1);
    }, 2000);
  };

  const current = questions[currentQuestion];

  if (!current) {
    return (
      <div
        style={{
          minHeight: "100vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          background: "linear-gradient(to bottom, #34ace0, #33d9b2)",
        }}
      >
        <div
          style={{
            textAlign: "center",
            backgroundColor: "#fff",
            padding: "30px",
            borderRadius: "20px",
            boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
          }}
        >
          <h1 style={{ fontSize: "2.5rem", color: "#4CAF50" }}>
            Quiz Completed!
          </h1>
          <p style={{ fontSize: "1.5rem", color: "#333" }}>
            Your final score: <strong>{score}</strong>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",
      }}
    >
      <div
        style={{
          minWidth: "730px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
          padding: "7px",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
            <Player
              autoplay
              loop
              src="https://fonts.gstatic.com/s/e/notoemoji/latest/2728/lottie.json"
              style={{ height: "40px", width: "40px" }}
            />
          </h1>
        </div>{" "}
        <div
          style={{
            padding: "20px",
            fontFamily: "Arial, sans-serif",
            color: "black",
          }}
        >
          <h2>
            <strong style={{ fontSize: "30px" }}>
              Question {currentQuestion + 1}
            </strong>
          </h2>
          <div style={{ maxWidth: "730px", textAlign: "center" }}>
            <p style={{ whiteSpace: "pre-wrap" }}>{current.question}</p>
          </div>

          <ul style={{ listStyle: "none", padding: 0 }}>
            {current.options.map((option, index) => {
              // Determine background color based on feedback state
              let bgColor = "#f8f9fa"; // Default color
              if (showFeedback) {
                if (option.isCorrect) bgColor = "#4CAF50"; // Green for correct
                else if (index === selectedOption) bgColor = "#F44336"; // Red for incorrect
              }

              return (
                <li
                  key={index}
                  onClick={() =>
                    !showFeedback && handleOptionClick(option, index)
                  }
                  style={{
                    backgroundColor: bgColor,
                    color: showFeedback ? "black" : "#333",
                    padding: "15px",
                    margin: "10px 0",
                    cursor: showFeedback ? "default" : "pointer",
                    border: "1px solid #ddd",
                    borderRadius: "8px",
                    fontSize: "1rem",
                    transition: "background-color 0.3s ease, color 0.3s ease",
                  }}
                >
                  {option.text}
                </li>
              );
            })}
          </ul>

          {showFeedback && (
            <p
              style={{
                color:
                  selectedOption !== null &&
                  current.options[selectedOption]?.isCorrect
                    ? "#4CAF50"
                    : "#F44336",
                fontSize: "1.5rem",
              }}
            >
              {current.options[selectedOption]?.isCorrect
                ? "Correct Answer!"
                : "Wrong Answer!"}
            </p>
          )}

          <p>
            <strong>Score: {score}</strong>
          </p>
        </div>
      </div>
    </div>
  );
}
