import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const GodOfThunder = () => {
  const [code, setCode] = useState("Enter your age:\n");
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [animatedCode, setAnimatedCode] = useState("");
  const [isRetry, setIsRetry] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [showOutput, setShowOutput] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [age, setAge] = useState(null);
  const outputRef = useRef(null);

  const typedText = useTypingAnimation("GOD OF THUNDER - THOR ODINSON", 100);

  const goToNextStep = () => {
    if (userInput.trim() !== "") {
      runCode();
    }
  };

  const handleTyping = (newCode) => {
    const lines = newCode.split("\n");
    if (currentStep === 1) {
      const ageInput = lines[1]?.trim() || "";
      setUserInput(ageInput);
    } else if (currentStep === 2) {
      const ticketInput = lines[1]?.trim() || "";
      setUserInput(ticketInput);
    }
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      setUserInput(lines.slice(1).join("\n"));
    }
  };

  useEffect(() => {

    setAnimatedCode("");
    setIsAnimationCompleted(false);

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code]);

  const runCode = () => {
    try {
      let outputMessage = "";
      const trimmedInput = userInput.trim();

      if (currentStep === 1) {
        const ageInput = parseInt(trimmedInput, 10);
        if (isNaN(ageInput)) {
          outputMessage = "Error: Please enter a valid age.";
        } else {
          setAge(ageInput);
          setCode("Do you have a ticket? (yes/no):\n");
          setUserInput("");
          setCurrentStep(2);
          return;
        }
      } else if (currentStep === 2) {
        const hasTicket = trimmedInput.toLowerCase() === "yes";

        if (age >= 14 && hasTicket) {
          outputMessage = "Welcome aboard The Thunderbolt!";
          setShowConfetti(true);
        } else if (!hasTicket && age >= 14) {
          outputMessage = "You need a ticket to ride The Thunderbolt.";
        } else if (hasTicket && age < 14) {
          outputMessage = "Sorry, you must be at least 14 years old to ride.";
        } else {
          outputMessage = "You don't meet the requirements to ride.";
        }
      }

      setIsRetry(true);
      setOutput(outputMessage);
      setShowOutput(true);

      setTimeout(() => setShowConfetti(false), 1000);

      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
    } catch (error) {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
    }
  };

  const handleRetry = () => {
    setCode("Enter your age:\n");
    setUserInput("");
    setOutput("");
    setCurrentStep(1);
    setAge(null);
    setIsRetry(false);
    setShowOutput(false);
    setShowConfetti(false);
    setAnimatedCode("");
    setIsAnimationCompleted(false);
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #283618, #bc6c25)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #283618, #bc6c25)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          LOGICAL OPERATORS
        </h2>
        <p className="text-lg text-gray-700">
          Another important concept we should cover is logical operators.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Logical operators, also known as Boolean operators, allow us to
          combine and evaluate multiple conditions. The main operators are{" "}
          <code className="font-mono text-blue-500">and</code>,{" "}
          <code className="font-mono text-blue-500">or</code>, and{" "}
          <code className="font-mono text-blue-500">not</code>:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>
              <code className="font-mono text-blue-500">and</code>
            </strong>{" "}
            returns True if both conditions are True, and returns False
            otherwise.
          </li>
          <li>
            <strong>
              <code className="font-mono text-blue-500">or</code>
            </strong>{" "}
            returns True if at least one of the conditions is True, and False
            otherwise.
          </li>
          <li>
            <strong>
              <code className="font-mono text-blue-500">not</code>
            </strong>{" "}
            returns True if the condition is False, and vice versa.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          Let's look at some examples:
        </p>
        <div className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
          <pre className="whitespace-pre-wrap">
            {`
# Example 1
if popcorn > 0 and soda > 0:
  print("Ready for movie night!")

# Example 2
if saturday or sunday:
  print("It's the weekend! Time to relax.")

# Example 3
if temperature > 30 and not raining:
  print("Perfect day for the beach!")
            `}
          </pre>
        </div>
        <p className="mt-4 text-lg font-bold text-black">INSTRUCTIONS</p>
        <p className="text-sm text-gray-700 mt-2">
          You're in charge of setting up the entry system for a new ride at an
          amusement park called "The Thunderbolt" 🚀.
        </p>
        <p className="text-sm text-gray-700 mt-2">
          The ride has two requirements:
        </p>
        <ul className="text-sm text-gray-700">
          <li>1. The rider must be at least 14 years old.</li>
          <li>2. The rider must have a ticket.</li>
        </ul>
        <p className="text-sm text-gray-700 mt-2">
          Create a new file called{" "}
          <code className="font-mono text-blue-500">the_thunderbolt.py</code>.
        </p>
        <p className="text-sm text-gray-700 mt-2">
          Ask the user for their age and whether they have a ticket (True or
          False), and store these values in an{" "}
          <code className="font-mono text-blue-500">age</code> variable and a{" "}
          <code className="font-mono text-blue-500">ticket</code> variable.
        </p>
        <p className="text-sm text-gray-700 mt-2">
          Use a combination of relational and logical operators to create the
          rules:
        </p>
        <ul className="text-sm text-gray-700 mt-2">
          <li>
            If they are old enough and have a ticket, print "Welcome aboard The
            Thunderbolt!"
          </li>
          <li>
            If they have a ticket, but aren't old enough, print "Sorry, you must
            be at least 14 years old to ride."
          </li>
          <li>
            If they are old enough, but don't have a ticket, print "You need a
            ticket to ride The Thunderbolt."
          </li>
          <li>Else, print "You don't meet the requirements to ride."</li>
        </ul>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#8a946a",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#a4ac86",
              color: "#414833",
              minHeight: "150px",
              borderRadius: "10px",
              overflow: "auto",
              whiteSpace: "pre-wrap",
            }}
          />
        </div>
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "83%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#ecf39e",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#4f772d",
                }}
              >
                Output:
              </h2>
              <pre
                style={{
                  fontSize: "1rem",
                  color: "#4f772d",
                  marginTop: "10px",
                }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={
                isRetry
                  ? handleRetry
                  : currentStep === 1
                  ? goToNextStep
                  : runCode
              }
              disabled={!userInput.trim()}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: isRetry ? "#4F772D" : "#ECF39E",
                color: isRetry ? "#ECF39E" : "#4F772D",               
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              {isRetry ? "Retry" : currentStep === 1 ? "Next" : "Run Code 🎉"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GodOfThunder;
