import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const TheGoodTheBadAndTheElif = () => {
  const [code, setCode] = useState(`Say something: \n`);
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);
  const [buttonClicked, setButtonClicked] = useState(false);
  const typedText = useTypingAnimation("WORD MAGIC WITH NLP", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  const codeBlockStyle = {
    backgroundColor: "#f7fafc", // Light gray background
    padding: "16px", // Padding for space around the code
    borderRadius: "8px", // Rounded corners
    color: "#2d3748", // Text color
    marginTop: "16px", // Top margin for spacing
    whiteSpace: "pre-wrap", // Preserve spaces and newlines
    wordWrap: "break-word", // Allow long words to break into the next line
    fontFamily: "monospace", // Monospace font for code
    fontSize: "16px", // Slightly larger font size for readability
  };

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);

  const runCode = () => {
    try {
      const greeting = userInput.trim().toLowerCase();

      if (!greeting) {
        setOutput("Error: Please provide an input.");
        setShowOutput(true);
        setShowConfetti(false);
        return;
      }

      let confetti = false;

      if (greeting.includes("hello")) {
        setOutput("Hello, human! 👋");
        confetti = true; // Enable confetti if the output is "Hello, human! 👋"
      } else {
        setOutput("I don't understand, but I'm learning! 🤖");
        confetti = false; // No confetti for other outputs
      }

      scrollToOutput();
      setShowOutput(true);
      setShowConfetti(confetti); // Trigger confetti if applicable

      if (confetti) {
        // Stop confetti after 1 second
        setTimeout(() => setShowConfetti(false), 1000);
      }
    } catch (error) {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
      setShowConfetti(false);
      scrollToOutput();
    }
  };

  const scrollToOutput = () => {
    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      const prompt = "Say something: ";
      const inputWithoutPrompt = lines.slice(1).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #92b4a7, #81667a)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #92b4a7, #81667a)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700 mb-6">
           Hello! مرحبًا! Bonjour! ¡Hola! こんにちは！ 안녕하세요!
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <p className="mt-6 text-lg font-bold text-black">INSTRUCTIONS</p>
        <ol className="list-decimal list-inside text-md pl-6 mt-2 text-gray-700">
          <li>
            <strong>
              Set up{" "}
              <a href="https://replit.com/" className="text-blue-500 underline">
                Replit
              </a>
            </strong>
            : Create a new file called{" "}
            <code className="font-mono text-blue-500">translator.py</code>.
          </li>
          <li>
            <strong>Install Googletrans</strong>: In the Replit terminal, type:
            <pre className="bg-gray-100 text-sm p-2 mt-4 mb-4">
              pip install googletrans==4.0.0-rc1
            </pre>
          </li>
          <li>
            <strong>Write the code</strong>:
          </li>
        </ol>
        <pre style={codeBlockStyle}>
          {`from googletrans import Translator

translator = Translator()

text = input("Enter text to translate: ")
lang = input("Enter the language code (e.g., 'es' for Spanish, 'fr' for French): ")

translation = translator.translate(text, dest=lang)

print(f"Translation: {translation.text}")`}
        </pre>
        <p className="text-sm text-gray-700 mt-2">
          Run the code: Try translating sentences into different languages!
        </p>
      </div>
    </div>
  );
};

export default TheGoodTheBadAndTheElif;
