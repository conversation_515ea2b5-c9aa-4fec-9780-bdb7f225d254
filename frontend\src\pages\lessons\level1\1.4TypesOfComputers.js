import React, { useState } from "react";
import Image from "next/image";

const TypesOfComputers = () => {
  const [matches, setMatches] = useState({});
  const [isDragging, setIsDragging] = useState(null);
  const [feedback, setFeedback] = useState(null);

  const computerTypes = [
    { id: 1, name: "Desktop Computers", image: "/lvl1_img/desktop.webp" },
    { id: 2, name: "Laptop Computers", image: "/lvl1_img/laptop.webp" },
    { id: 3, name: "Tablet Computers", image: "/lvl1_img/tablet.webp" },
    { id: 4, name: "Smartphones", image: "/lvl1_img/phone.webp" },
  ];

  const purposes = [
    { id: "a", label: "Large-screen productivity" },
    { id: "b", label: "Portable computing" },
    { id: "c", label: "Mobile communication and gaming" },
    { id: "d", label: "Touchscreen convenience" },
  ];

  const correctMatches = {
    a: 1,
    b: 2,
    c: 4,
    d: 3,
  };

  const handleDragStart = (id) => {
    setIsDragging(id);
  };

  const handleDrop = (purposeId) => {
    if (isDragging) {
      setMatches((prev) => ({ ...prev, [purposeId]: isDragging }));
      setIsDragging(null);
    }
  };

  const checkAnswers = () => {
    const results = Object.keys(correctMatches).map((purposeId) => ({
      purpose: purposes.find((purpose) => purpose.id === purposeId).label,
      isCorrect: correctMatches[purposeId] === matches[purposeId],
    }));

    const allCorrect = results.every((result) => result.isCorrect);

    setFeedback({
      message: allCorrect
        ? "🎉 Great job! All matches are correct!"
        : "❌ Some matches are incorrect. Try again!",
      results,
      allCorrect,
    });
  };

  const retry = () => {
    setMatches({});
    setFeedback(null);
  };

  return (
    <div className="text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-100 p-6 rounded-lg shadow-lg">
      {/* Header Section */}
      <h1 className="text-5xl font-bold mb-6 text-purple-700 text-center">
        Different Types of Computers
      </h1>
      <p className="text-lg text-center mb-6">
        Computers come in many shapes and sizes, each designed for specific tasks. Let’s learn about them and match them to their purposes!
      </p>
      <div className="mt-4 flex justify-center items-center">
        <Image
          src="/lvl1_img/Untitled (4).png"
          alt="A fun computer illustration"
          width={500}
          height={300}
          className="rounded-lg shadow-md"
        />
      </div>

      {/* Drag-and-Drop Activity */}
      <div className="mt-10 grid grid-cols-2 gap-8">
        {/* Draggable Items */}
        <div>
          <h2 className="text-2xl font-semibold mb-4 text-center">
            Computer Types
          </h2>
          <div className="space-y-6 flex flex-col items-center">
            {computerTypes.map(
              (type) =>
                !Object.values(matches).includes(type.id) && (
                  <div
                    key={type.id}
                    draggable
                    onDragStart={() => handleDragStart(type.id)}
                    className="flex flex-col items-center p-4 border border-gray-300 bg-white rounded-md shadow-md hover:shadow-lg cursor-pointer"
                  >
                    <img
                      src={type.image}
                      alt={type.name}
                      className="w-32 h-32 mb-2"
                    />
                    <p className="text-center font-semibold">{type.name}</p>
                  </div>
                )
            )}
          </div>
        </div>

        {/* Drop Zones */}
        <div>
          <h2 className="text-2xl font-semibold mb-4 text-center">
            Computer Purposes
          </h2>
          <div className="space-y-6 flex flex-col items-center">
            {purposes.map((purpose) => (
              <div
                key={purpose.id}
                onDrop={() => handleDrop(purpose.id)}
                onDragOver={(e) => e.preventDefault()}
                className="flex flex-col items-center justify-center w-36 h-36 border-2 border-dashed border-blue-500 rounded-md bg-blue-50 shadow-inner"
              >
                {matches[purpose.id] ? (
                  <div className="flex flex-col items-center">
                    <img
                      src={
                        computerTypes.find(
                          (type) => type.id === matches[purpose.id]
                        ).image
                      }
                      alt="Matched Computer"
                      className="w-28 h-28 mb-2"
                    />
                  </div>
                ) : (
                  <p className="text-blue-700 font-semibold text-center">
                    {purpose.label}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Buttons and Feedback */}
      <div className="mt-8 text-center">
        <button
          onClick={checkAnswers}
          className="px-6 py-3 bg-purple-600 text-white font-bold rounded-lg shadow-md hover:bg-purple-700 transition-all"
        >
          Check Answers
        </button>
        <button
          onClick={retry}
          className="ml-4 px-6 py-3 bg-gray-600 text-white font-bold rounded-lg shadow-md hover:bg-gray-700 transition-all"
        >
          Retry
        </button>
      </div>

      {feedback && (
        <div
          className={`mt-8 p-4 rounded-lg shadow-md ${
            feedback.allCorrect ? "bg-green-100" : "bg-yellow-100"
          }`}
        >
          <p
            className={`text-lg font-bold text-center ${
              feedback.allCorrect ? "text-green-700" : "text-yellow-700"
            }`}
          >
            {feedback.message}
          </p>
          {!feedback.allCorrect && (
            <ul className="list-disc pl-6 mt-4">
              {feedback.results.map((result, index) => (
                <li
                  key={index}
                  className={`${
                    result.isCorrect ? "text-green-700" : "text-red-700"
                  }`}
                >
                  {result.purpose}:{" "}
                  {result.isCorrect ? "Correct!" : "Incorrect"}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default TypesOfComputers;
