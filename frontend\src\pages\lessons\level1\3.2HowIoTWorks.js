import React, { useState } from "react";

const IoTHowItWorksActivity = () => {
  const [matches, setMatches] = useState({});
  const [feedback, setFeedback] = useState("");

  const items = [
    { id: "Smart Devices", label: "Smart Devices", icon: "📱" },
    { id: "IoT Applications", label: "IoT Applications", icon: "🧠" },
    { id: "Graphical User Interface", label: "Graphical User Interface", icon: "💻" },
  ];

  const descriptions = [
    {
      id: "Smart Devices",
      label: "Everyday items like your TV, toys, or even cars are equipped with sensors to collect data.",
    },
    {
      id: "IoT Applications",
      label: "Applications analyze the data collected and decide what action to take.",
    },
    {
      id: "Graphical User Interface",
      label: "You control everything using an app on your phone or computer.",
    },
  ];

  const correctMatches = {
    "Smart Devices": "Smart Devices",
    "IoT Applications": "IoT Applications",
    "Graphical User Interface": "Graphical User Interface",
  };

  const handleDragStart = (id) => {
    setFeedback(""); // Clear feedback while dragging
    setMatches((prev) => ({ ...prev, dragging: id }));
  };

  const handleDrop = (descriptionId) => {
    const draggingId = matches.dragging;
    if (draggingId) {
      setMatches((prev) => ({
        ...prev,
        [descriptionId]: draggingId,
        dragging: null,
      }));
    }
  };

  const checkAnswers = () => {
    const results = Object.keys(correctMatches).map((descriptionId) => ({
      description: descriptionId,
      isCorrect: correctMatches[descriptionId] === matches[descriptionId],
    }));

    const allCorrect = results.every((result) => result.isCorrect);

    setFeedback(
      allCorrect
        ? "🎉 Great job! All matches are correct!"
        : "❌ Some matches are incorrect. Try again!"
    );
  };

  const resetActivity = () => {
    setMatches({});
    setFeedback("");
  };

  return (
    <div className="flex flex-col items-center text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-50 p-6 min-h-screen">
      {/* Heading Section */}
      <h1 className="text-5xl font-bold mb-6 text-purple-700">
        How IoT Works
      </h1>
      <p className="text-xl text-center mb-4">
        IoT works by connecting devices that communicate with each other over the internet. Let’s explore its components:
      </p>

      {/* Instructions */}
      <ul className="list-disc pl-6 mb-6 text-lg text-left max-w-4xl">
        <li>
          <strong>Smart Devices:</strong> Everyday items like TVs, toys, or cars with sensors that collect data.
        </li>
        <li>
          <strong>IoT Applications:</strong> Analyze data and take actions like turning on lights automatically.
        </li>
        <li>
          <strong>Graphical User Interface:</strong> Lets you control devices using apps on your phone or computer.
        </li>
        <p className="mt-4">
          <strong>Example:</strong> Your smart garden can water plants when it detects they are dry. Amazing, right?
        </p>
      </ul>

      {/* Activity Section */}
      <div className="grid grid-cols-2 gap-8 w-full max-w-5xl">
        {/* Drag Items */}
        <div>
          <h2 className="text-2xl font-bold mb-4 text-blue-600">Components</h2>
          <div className="space-y-6">
            {items.map((item) => (
              <div
                key={item.id}
                draggable
                onDragStart={() => handleDragStart(item.id)}
                className="flex items-center justify-center w-56 h-20 bg-white border-2 border-gray-300 rounded-md shadow-sm cursor-pointer hover:shadow-lg transition"
              >
                <span className="text-2xl mr-2">{item.icon}</span>
                <span className="text-lg">{item.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Drop Zones */}
        <div>
          <h2 className="text-2xl font-bold mb-4 text-green-600">Descriptions</h2>
          <div className="space-y-8">
            {descriptions.map((description) => (
              <div
                key={description.id}
                onDrop={() => handleDrop(description.id)}
                onDragOver={(e) => e.preventDefault()}
                className="w-full h-24 bg-gray-50 border-2 border-dashed border-blue-500 rounded-md p-2 flex items-center justify-center hover:bg-blue-50 transition"
              >
                {matches[description.id] ? (
                  <div className="w-full h-full bg-green-100 rounded-md flex items-center justify-center text-center">
                    <span>{matches[description.id]}</span>
                  </div>
                ) : (
                  <p className="text-gray-600 text-center">{description.label}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Buttons */}
      <div className="mt-8 flex space-x-4">
        <button
          onClick={checkAnswers}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition"
        >
          Check Answers
        </button>
        <button
          onClick={resetActivity}
          className="px-6 py-3 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition"
        >
          Reset
        </button>
      </div>

      {/* Feedback Section */}
      {feedback && (
        <div
          className={`mt-6 px-4 py-2 rounded-lg shadow-md ${
            feedback.includes("Great")
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          <p className="text-lg font-bold">{feedback}</p>
        </div>
      )}
    </div>
  );
};

export default IoTHowItWorksActivity;
