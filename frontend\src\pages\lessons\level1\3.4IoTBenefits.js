import React, { useState } from "react";

const IoTBenefits = () => {
  const [unlockedBenefits, setUnlockedBenefits] = useState([]);

  const benefits = [
    {
      id: "Innovation",
      label: "Innovation 💡",
      description: "Businesses can create smarter products based on what customers need.",
    },
    {
      id: "Efficiency",
      label: "Efficiency ⚙️",
      description: "IoT helps make tasks faster and more productive, like keeping factories running smoothly.",
    },
    {
      id: "Safety",
      label: "Safety 🛡️",
      description: "IoT devices can monitor workplaces and send alerts if something is wrong.",
    },
  ];

  const handleUnlock = (id) => {
    if (!unlockedBenefits.includes(id)) {
      setUnlockedBenefits((prev) => [...prev, id]);
    }
  };

  return (
    <div className="text-gray-700 min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-50 p-6">
      <h1 className="text-5xl font-bold mb-6 text-purple-700 text-center">Benefits of IoT</h1>
      <p className="text-lg text-center mb-8">
        Discover how IoT makes life better! Click on the colorful icons to unlock and learn about each benefit.
      </p>

      {/* Icon Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 justify-center items-center mb-12">
        {benefits.map((benefit) => (
          <div
            key={benefit.id}
            onClick={() => handleUnlock(benefit.id)}
            className={`p-6 w-full h-40 flex flex-col items-center justify-center cursor-pointer rounded-lg shadow-lg transform hover:scale-105 transition-transform ${
              unlockedBenefits.includes(benefit.id)
                ? "bg-green-100 border-4 border-green-500"
                : "bg-gray-100 border-4 border-gray-300"
            }`}
          >
            <span className="text-5xl">{benefit.label.split(" ")[1]}</span>
            <p className="mt-4 font-bold text-xl text-gray-800">{benefit.label.split(" ")[0]}</p>
          </div>
        ))}
      </div>

      {/* Unlocked Benefits Section */}
      <div className="bg-blue-100 p-6 border-l-4 border-blue-500 rounded-lg shadow-lg mb-8">
        <h2 className="text-3xl font-semibold mb-4 text-blue-700">Unlocked Benefits</h2>
        {unlockedBenefits.length > 0 ? (
          <ul className="list-disc pl-6 space-y-4 text-lg">
            {benefits
              .filter((benefit) => unlockedBenefits.includes(benefit.id))
              .map((benefit) => (
                <li key={benefit.id} className="text-gray-800">
                  <strong>{benefit.label.split(" ")[0]}:</strong> {benefit.description}
                </li>
              ))}
          </ul>
        ) : (
          <p className="text-gray-600">Click on the icons above to reveal the benefits of IoT!</p>
        )}
      </div>

      {/* Creative Activity Section */}
      <div className="bg-yellow-100 p-6 border-l-4 border-yellow-500 rounded-lg shadow-lg">
        <h2 className="text-2xl font-semibold mb-4 text-yellow-700">Creative Activity</h2>
        <p className="text-lg">
          Imagine you could invent an IoT device to help with your daily chores, like cleaning your room or finishing homework. What would
          it do? Write your idea on paper and share it with your friends or family!
        </p>
      </div>
    </div>
  );
};

export default IoTBenefits;
