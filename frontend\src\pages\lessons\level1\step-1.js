import React from "react";
import Link from "next/link";

const StepOne = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 1: What is Scratch?</h1>
        <p className="text-center mt-2 text-lg">
          Let’s explore Scratch, the coolest way to start coding!
        </p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What is Scratch?</h2>
          <p className="text-lg mb-4">
            <strong>Scratch</strong> is a coding platform designed just for you! It's like building with LEGO blocks, but instead of
            building towers, you’re building games, animations, and stories. Scratch makes coding fun, easy, and super exciting!
          </p>
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg mb-6">
            <p className="text-lg font-semibold">Why is Scratch amazing?</p>
            <ul className="list-disc pl-6 mt-2">
              <li>Drag-and-drop blocks make it easy to code without typing.</li>
              <li>You can create cool animations, games, and interactive stories.</li>
              <li>Share your projects and get inspired by others!</li>
            </ul>
          </div>

          {/* Fun Illustration */}
          <div className="flex justify-center mb-6">
            <img
              src="/lvl1_img/download.png"
              alt="Scratch Cat"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Activity Section */}
          <div className="bg-blue-100 p-4 border-l-4 border-blue-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Explore Scratch!</h3>
            <p className="text-lg mb-4">
              Visit the Scratch website and explore the projects gallery. See what other kids are creating and try it yourself!
            </p>
            <a
              href="https://scratch.mit.edu"
              target="_blank"
              rel="noreferrer"
              className="inline-block px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              Go to Scratch
            </a>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level-1">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back to Home</button>
        </Link>
        <Link href="/lessons/level1/step-2">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 2</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepOne;
