import React from "react";
import Link from "next/link";

const StepFour = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 4: Add More Actions</h1>
        <p className="text-center mt-2 text-lg">Let’s make your Scratch project even cooler!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">Make Your Sprite Move and Talk!</h2>
          <p className="text-lg mb-4">
            In this step, you’ll learn how to make your sprite move, play sounds, or even change costumes. Follow these steps to add more
            fun actions to your project:
          </p>

          {/* Step-by-Step Instructions */}
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Make Your Sprite Move:</strong> 
              <ul className="list-disc pl-6 mt-2">
                <li>Go to the Motion section in Scratch.</li>
                <li>Drag the <span className="text-purple-700 font-semibold">"move 10 steps"</span> block under your existing blocks.</li>
                <li>Click the green flag and watch your sprite move!</li>
              </ul>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 142645.png"
                  alt="Placeholder: Move Steps"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Add Sound:</strong>
              <ul className="list-disc pl-6 mt-2">
                <li>Go to the Sound section and drag the <span className="text-purple-700 font-semibold">"play sound [meow]"</span> block into the script.</li>
                <li>Test it by clicking the green flag!</li>
              </ul>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 142705.png"
                  alt="Placeholder: Add Sound"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Change Costume:</strong>
              <ul className="list-disc pl-6 mt-2">
                <li>Go to the Looks section.</li>
                <li>Drag the <span className="text-purple-700 font-semibold">"next costume"</span> block into your script.</li>
                <li>Test it by clicking the green flag and watch your sprite change costumes!</li>
              </ul>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 142746.png"
                  alt="Placeholder: Change Costume"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 144406.png"
              alt="Scratch Sprite Actions"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Challenge: Create a Dance Party!</h3>
            <p className="text-lg mb-4">
              Use the "move," "sound," and "costume" blocks to make your sprite dance. Try adding multiple costumes, changing the steps, or
              even playing different sounds. Let’s see how creative you can get!
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-3">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 3</button>
        </Link>
        <Link href="/lessons/level1/step-5">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 5</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepFour;
