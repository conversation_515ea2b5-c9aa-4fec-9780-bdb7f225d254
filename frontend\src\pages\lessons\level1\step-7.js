import React from "react";
import Link from "next/link";

const StepSeven = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 7: Costumes and Backdrops</h1>
        <p className="text-center mt-2 text-lg">
          Let’s make your project look awesome with costumes and backdrops!
        </p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What Are Costumes and Backdrops?</h2>
          <p className="text-lg mb-4">
            Costumes are like outfits for your sprites. They let you change how your sprite looks or animate them. Backdrops are the
            backgrounds of your project, setting the scene for your story or game.
          </p>

          {/* Features of Costumes */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Costumes:</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Switch Looks:</strong> Sprites can have multiple costumes, so they can change their look during your project.
            </li>
            <li>
              <strong>Animate:</strong> Switching costumes quickly can make your sprite look like it’s moving or dancing.
            </li>
            <li>
              <strong>Create Your Own:</strong> You can draw costumes in the Scratch editor or upload your own images.
            </li>
          </ul>

          {/* Features of Backdrops */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Backdrops:</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Set the Scene:</strong> Backdrops create the environment for your story, game, or animation.
            </li>
            <li>
              <strong>Switch Backdrops:</strong> Change backdrops during your project to show different scenes or times of day.
            </li>
          </ul>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 143044.png"
              alt="Costumes and Backdrops"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Add Costumes and Backdrops:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Add a Costume:</strong>
              <p className="text-lg mt-2">
                Click on the "Costumes" tab at the top of the Scratch editor. Choose a new costume from the library, draw your own, or
                upload an image.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 150631.png"
                  alt="Placeholder: Add Costume"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Add a Backdrop:</strong>
              <p className="text-lg mt-2">
                Click on the "Choose a Backdrop" button at the bottom-right corner of the editor. Select a backdrop from the library, draw
                one, or upload an image.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 150800.png"
                  alt="Placeholder: Add Backdrop"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Switch Costumes or Backdrops:</strong>
              <p className="text-lg mt-2">
                Use the "next costume" or "switch backdrop to [backdrop]" blocks in the Looks category to change costumes or backdrops in
                your project.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 150850.png"
                  alt="Placeholder: Switch Costume or Backdrop"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Create a Scene!</h3>
            <p className="text-lg mb-4">
              Add costumes to your sprite and create a backdrop for your project. Try switching costumes to make an animation or changing
              backdrops to tell a story!
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-6">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 6</button>
        </Link>
        <Link href="/lessons/level1/step-8">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 8</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepSeven;
