import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "../level-4/section1/animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Sportsgames = () => {
  const typedText = useTypingAnimation("Cybersecurity", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>What is Quantum Computing?</strong>
        </p>
        <img
          src="/path-to-quantum-computing-image.png"
          alt="Quantum Computing"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Quantum computing uses super-smart computers that can solve very
          complex problems quickly by thinking in many ways at once.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example:</strong> Quantum computers can help discover new
          medicines or solve tricky scientific problems.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Fun Facts:</strong>
        </p>
        <img
          src="/path-to-quantum-fun-facts-image.png"
          alt="Fun Facts"
          className="mb-4"
        />
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            Quantum computers use qubits, which can represent both 0 and 1 at
            the same time.
          </li>
          <li>The first quantum computer was developed in the early 2000s.</li>
          <li>
            Quantum computers need to be kept at extremely low temperatures,
            close to absolute zero (-273.15°C or -459.67°F), to function
            properly.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Real-World Examples:</strong>
        </p>
        <img
          src="/path-to-quantum-examples-image.png"
          alt="Real-World Examples"
          className="mb-4"
        />
        <ol className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Medicine:</strong> Quantum computers can analyze complex
            molecules and simulate how they interact, helping scientists
            discover new drugs and treatments faster.
          </li>
          <li>
            <strong>Science:</strong> Quantum computers can solve complex
            problems in physics and chemistry, helping us understand the
            universe better and develop new materials.
          </li>
          <li>
            <strong>Technology:</strong> Quantum computing can enhance
            artificial intelligence by processing vast amounts of data quickly,
            leading to smarter and more efficient AI systems.
          </li>
        </ol>
        <p className="text-lg text-gray-700 mb-4">
          <strong>
            How is a Quantum Computer Different from a Regular Computer?
          </strong>
        </p>
        <img
          src="/path-to-computer-comparison-image.png"
          alt="Quantum vs Regular Computers"
          className="mb-4"
        />
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Qubits vs. Bits:</strong>
            <ul>
              <li>
                <strong>Regular Computers:</strong> Use bits, which can be
                either 0 or 1.
              </li>
              <li>
                <strong>Quantum Computers:</strong> Use qubits, which can be
                both 0 and 1 at the same time (superposition).
              </li>
            </ul>
          </li>
          <li>
            <strong>Processing Power:</strong>
            <ul>
              <li>
                <strong>Regular Computers:</strong> Process data in a linear
                sequence, one step at a time.
              </li>
              <li>
                <strong>Quantum Computers:</strong> Can process many
                possibilities simultaneously, making them much faster for
                certain types of problems.
              </li>
            </ul>
          </li>
          <li>
            <strong>Entanglement:</strong>
            <ul>
              <li>
                <strong>Regular Computers:</strong> Bits operate independently.
              </li>
              <li>
                <strong>Quantum Computers:</strong> Qubits can be entangled,
                meaning the state of one qubit is linked to the state of
                another, even if they are far apart. This allows for complex,
                coordinated calculations.
              </li>
            </ul>
          </li>
          <li>
            <strong>Temperature:</strong>
            <ul>
              <li>
                <strong>Regular Computers:</strong> Operate at normal room
                temperatures.
              </li>
              <li>
                <strong>Quantum Computers:</strong> Need to be kept at extremely
                low temperatures, close to absolute zero, to maintain qubit
                stability.
              </li>
            </ul>
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Learning Points:</strong>
        </p>
        <img
          src="/path-to-learning-points-image.png"
          alt="Learning Points"
          className="mb-4"
        />
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Superposition and Entanglement:</strong> Quantum computers
            use these principles, allowing qubits to exist in multiple states at
            once and to be linked together in powerful ways.
          </li>
          <li>
            <strong>Extreme Temperatures:</strong> Quantum computers need to be
            stored at very low temperatures to keep their qubits stable and
            functional. This is done using special refrigerators called dilution
            refrigerators, which cool the quantum processors to fractions of a
            degree above absolute zero.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Summary:</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          In this chapter, we've learned about the fascinating world of Quantum
          Computing. We discovered that quantum computing uses super-smart
          computers called quantum computers that can solve very complex
          problems quickly by thinking in many ways at once. We saw real-world
          examples like drug discovery and AI improvement, and we learned how
          quantum computers use qubits and need to be kept at extremely low
          temperatures. By solving a puzzle and answering review questions, we
          now understand how quantum computing can revolutionize various fields
          and its incredible potential. Keep exploring and see what amazing
          discoveries quantum computing can lead to!
        </p>
      </div>
    </div>
  );
};

export default Sportsgames;
