import React from 'react';

export default function DataVisualization() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Data Visualization</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Data Visualization is the art of turning data into a story. Using <span className="highlight">charts</span>, <span className="highlight">graphs</span>, and <span className="highlight">maps</span>, we can make numbers and patterns easy to understand.
            </p>

            {/* Example 1: Bar Charts */}
            <div className="content-row">
                <img
                    src="/bar_chart1.png" /* Replace with your actual image path */
                    alt="Bar Chart Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📊 Bar Charts</h2>
                    <p className="text-lg">
                        Bar charts are perfect for comparing categories. For example:
                        <br />
                        - A school compares student attendance across different classes.<br />
                        - A company tracks monthly sales by product.
                    </p>
                </div>
            </div>

            {/* Example 2: Line Charts */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📈 Line Charts</h2>
                    <p className="text-lg">
                        Line charts show trends over time. For instance:
                        <br />
                        - A fitness app tracks your weight changes over months.<br />
                        - A weather service monitors temperature fluctuations daily.
                    </p>
                </div>
                <img
                    src="/line_charts1.png" /* Replace with your actual image path */
                    alt="Line Chart Example"
                    className="image-placeholder"
                />
            </div>

            {/* Example 3: Pie Charts */}
            <div className="content-row">
                <img
                    src="/pie_charts1.png" /* Replace with your actual image path */
                    alt="Pie Chart Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🥧 Pie Charts</h2>
                    <p className="text-lg">
                        Pie charts are great for showing proportions. For example:
                        <br />
                        - A company visualizes the market share of competitors.<br />
                        - A survey shows the percentage of people who prefer different ice cream flavors.
                    </p>
                </div>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                Data Visualization makes complex information simple and beautiful. Ready to create your own visual stories?
            </p>
        </div>
    );
}
