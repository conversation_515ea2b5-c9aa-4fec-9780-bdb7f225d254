import React from 'react';

export default function IntroductionToCharts() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-500 via-purple-600 to-indigo-800 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Introduction to Charts</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Charts like <span className="highlight">bar graphs</span>, <span className="highlight">pie charts</span>, and <span className="highlight">line graphs</span> are powerful tools for visualizing trends and patterns in data.
            </p>

            {/* Example 1: Bar Graph */}
            <div className="content-row">
                <img
                    src="/bar_chart_example.jpg" /* Replace with your actual image path */
                    alt="Bar Chart Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📊 Bar Graphs</h2>
                    <p className="text-lg">
                        Bar graphs are great for comparing different categories. For example:
                        <br />
                        - Sales of different products in a store.<br />
                        - Student attendance across various classes.
                    </p>
                </div>
            </div>

            {/* Example 2: Pie Chart */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🥧 Pie Charts</h2>
                    <p className="text-lg">
                        Pie charts show proportions and percentages. For instance:
                        <br />
                        - How a company spends its budget.<br />
                        - The share of users across different social media platforms.
                    </p>
                </div>
                <img
                    src="/pie_chart_example.jpg" /* Replace with your actual image path */
                    alt="Pie Chart Example"
                    className="image-placeholder"
                />
            </div>

            {/* Example 3: Line Graph */}
            <div className="content-row">
                <img
                    src="/line_chart_example.jpg" /* Replace with your actual image path */
                    alt="Line Graph Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📈 Line Graphs</h2>
                    <p className="text-lg">
                        Line graphs are perfect for showing trends over time. For example:
                        <br />
                        - Temperature changes over a week.<br />
                        - Monthly revenue of a business.
                    </p>
                </div>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                Charts make data more engaging and easier to understand. Start creating charts to tell your data story!
            </p>
        </div>
    );
}
