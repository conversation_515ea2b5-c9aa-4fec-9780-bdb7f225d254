import React, { useState } from "react";

const Quiz = () => {
  const questions = [
    // Introduction to Computers
    {
      question: "What are the four main processes that a computer goes through to handle information?",
      options: [
        "Input, Processing, Output, Storage",
        "Typing, Clicking, Saving, Printing",
        "Sending, Receiving, Loading, Playing",
        "Uploading, Downloading, Editing, Deleting",
      ],
      answer: "Input, Processing, Output, Storage",
      hint: "Think about how a computer receives, processes, and outputs information.",
    },
    {
      question: "What is the difference between hardware and software?",
      options: [
        "Hardware is what you can see; software is what you can hear.",
        "Hardware is the programs; software is the physical parts.",
        "Hardware is the physical parts; software is the programs.",
        "Hardware is the information; software is the storage.",
      ],
      answer: "Hardware is the physical parts; software is the programs.",
      hint: "Hardware is tangible; software isn't.",
    },
    {
      question: "Which type of computer is small enough to fit in your pocket?",
      options: [
        "Desktop Computer",
        "Laptop Computer",
        "Tablet Computer",
        "Smartphone",
      ],
      answer: "Smartphone",
      hint: "It's the most portable and can make calls.",
    },

    // Scratch
    {
      question: "What do you use to create code in Scratch?",
      options: [
        "Text commands",
        "Colorful blocks that snap together",
        "Handwritten notes",
        "Voice commands",
      ],
      answer: "Colorful blocks that snap together",
      hint: "It's visual and beginner-friendly.",
    },
    {
      question: "Which block would you use to make a sprite move to the left in Scratch?",
      options: [
        "move x by 10",
        "change y by -10",
        "move x by -10",
        "turn 15 degrees",
      ],
      answer: "move x by -10",
      hint: "Negative values decrease; x controls horizontal movement.",
    },

    // IoT
    {
      question: "What does IoT stand for?",
      options: [
        "Internet of Toys",
        "Internet of Technology",
        "Internet of Things",
        "Internet of Telecommunication",
      ],
      answer: "Internet of Things",
      hint: "It connects devices to the internet.",
    },
    {
      question: "How do IoT devices collect and share information?",
      options: ["Wires and cables", "Sensors", "Microphones", "Remote controls"],
      answer: "Sensors",
      hint: "Devices need something to detect changes in the environment.",
    },

    // Computer Vision
    {
      question: "What is Computer Vision?",
      options: [
        "A type of video game",
        "A field of AI that helps computers see and understand images",
        "A new type of camera",
        "A way to listen to music on a computer",
      ],
      answer: "A field of AI that helps computers see and understand images",
      hint: "Think about AI interpreting visual data.",
    },
    {
      question: "What do computers use to 'see' the world in Computer Vision?",
      options: [
        "Microphones to hear sounds",
        "Cameras and sensors to capture images and videos",
        "Smelling different objects",
        "Reading books",
      ],
      answer: "Cameras and sensors to capture images and videos",
      hint: "It involves visual input.",
    },

    // Additional Fun Questions
    {
      question: "Why do computers have fans inside them?",
      options: [
        "To play music",
        "To keep the processor cool",
        "To store files",
        "To display graphics",
      ],
      answer: "To keep the processor cool",
      hint: "It prevents overheating.",
    },
    {
      question: "What is an output device?",
      options: [
        "Keyboard",
        "Mouse",
        "Monitor",
        "Hard Drive",
      ],
      answer: "Monitor",
      hint: "It displays the results.",
    },
    {
      question: "What is an operating system?",
      options: [
        "The computer’s processor",
        "The physical parts of a computer",
        "The core software that helps other programs run smoothly",
        "The storage where files are saved",
      ],
      answer: "The core software that helps other programs run smoothly",
      hint: "It's essential for managing tasks.",
    },
    {
      question: "What does the processor do in a computer?",
      options: [
        "It stores files and documents.",
        "It connects to the internet.",
        "It prints documents.",
        "It processes information and handles tasks.",
      ],
      answer: "It processes information and handles tasks.",
      hint: "Think of it as the brain of the computer.",
    },
  ];

  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [feedback, setFeedback] = useState("");
  const [attempts, setAttempts] = useState(0);

  const handleAnswer = (selectedOption) => {
    if (selectedOption === questions[currentQuestion].answer) {
      setScore(score + 1);
      setFeedback("Correct! 🎉");
      setTimeout(() => {
        setFeedback("");
        setAttempts(0);
        if (currentQuestion < questions.length - 1) {
          setCurrentQuestion(currentQuestion + 1);
        }
      }, 2000);
    } else {
      setFeedback(`Wrong! Try again. Hint: ${questions[currentQuestion].hint}`);
      setAttempts(attempts + 1);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 via-white to-purple-100 p-8">
      <div className="max-w-4xl mx-auto bg-purple shadow-lg rounded-lg p-6">
        {currentQuestion < questions.length ? (
          <>
            <h1 className="text-4xl font-extrabold mb-6 text-purple-700 text-center">
              Quiz
            </h1>
            <p className="text-lg mb-4">
              Question {currentQuestion + 1} of {questions.length}
            </p>
            <p className="text-xl font-semibold mb-6 text-gray-800">
              {questions[currentQuestion].question}
            </p>
            <ul className="space-y-4">
              {questions[currentQuestion].options.map((option, index) => (
                <li key={index}>
                  <button
                    onClick={() => handleAnswer(option)}
                    className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-left"
                  >
                    {option}
                  </button>
                </li>
              ))}
            </ul>
            {feedback && (
              <div className="mt-6 p-4 text-center text-lg rounded bg-gray-100 text-gray-700 shadow">
                {feedback}
              </div>
            )}
          </>
        ) : (
          <div className="text-center">
            <h1 className="text-4xl font-extrabold mb-6 text-purple-700">
              Quiz Complete!
            </h1>
            <p className="text-lg mb-4">
              Your score is <strong>{score}</strong> out of{" "}
              <strong>{questions.length}</strong>.
            </p>
            <p className="mb-6">
              Fantastic effort! Keep learning and improving.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Quiz;
