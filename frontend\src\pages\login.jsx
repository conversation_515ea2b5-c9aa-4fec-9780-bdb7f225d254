'use client';

import { useState } from 'react';
import { useRouter } from 'next/router';
import { Rocket } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { authAPI } from '@/utils/api';

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await authAPI.login({ username, password });
      const { token, user } = response.data;

      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      router.push('/dashboard');
    } catch (error) {
      setError(error.response?.data?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#023148] flex flex-col items-center justify-center p-4">
      {/* Header */}
      <header className="flex flex-col md:flex-row md:items-center md:justify-between w-full px-4 mb-8">
        <div className="flex justify-center md:justify-start mb-4 md:mb-0">
          <Image src="/codesprint-logo.png" alt="CodeSprint Logo" width={150} height={50} />
        </div>
      </header>

      {/* Main Content */}
      <div className="flex flex-col md:flex-row items-center justify-center gap-6 w-full max-w-4xl">
        {/* Mascot and Comment */}
        <div className="flex flex-col justify-center items-center mb-5 md:mb-0 md:justify-start relative">
          <Image src="/glitch-mascot.png" alt="Glitch Mascot" width={400} height={400} />
          <p className="mt-4 text-lg text-[#FFC107] font-semibold text-center">
          Welcome back, Explorer! Let's unlock new coding galaxies together!
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-[#333] rounded-2xl shadow-xl p-8 max-w-md w-full border-4 border-[#FFC107] text-center">
          <h1 className="text-4xl font-bold text-white mb-2">CODE SPRINT</h1>
          <p className="text-gray-300 mt-4">Ready for a coding adventure?</p>
          <form onSubmit={handleLogin} className="space-y-6 text-teal-950 mt-6">
            <div className="text-left">
              <label htmlFor="username" className="block text-sm font-medium text-[#FFC107]">
                Astronaut Name
              </label>
              <input
                id="username"
                type="text"
                required
                className="mt-1 block w-full px-3 py-2 bg-[#555] border-2 border-[#FFC107] rounded-md text-sm shadow-sm placeholder-gray-400 text-white
                           focus:outline-none focus:border-[#FFC107] focus:ring-2 focus:ring-[#FFC107]"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your space name"
                disabled={loading}
              />
            </div>
            <div className="text-left">
              <label htmlFor="password" className="block text-sm font-medium text-[#FFC107]">
                Secret Code
              </label>
              <input
                id="password"
                type="password"
                required
                className="mt-1 block w-full px-3 py-2 bg-[#555] border-2 border-[#FFC107] rounded-md text-sm shadow-sm placeholder-gray-400 text-white
                           focus:outline-none focus:border-[#FFC107] focus:ring-2 focus:ring-[#FFC107]"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your secret code"
                disabled={loading}
              />
              {/* Forgot Password Link */}
              <div className="mt-2 text-right">
                <Link href="/forgot-password" className="text-sm text-[#FFC107] hover:text-[#FFB300]">
                  Forgot your secret code?
                </Link>
              </div>
            </div>
            {error && <p className="text-red-500 text-sm font-bold">{error}</p>}
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#FFC107] hover:bg-[#FFB300] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FFC107] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Rocket className="mr-2 h-5 w-5" />
              {loading ? 'Launching...' : 'Launch into Learning!'}
            </button>
          </form>
          <div className="mt-6">
            <p className="text-center text-sm text-gray-300">
              New space explorer?{' '}
              <Link href="/register" className="font-medium text-[#FFC107] hover:text-[#FFB300]">
                Join the crew!
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
