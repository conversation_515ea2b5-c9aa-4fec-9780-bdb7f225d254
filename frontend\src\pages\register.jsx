'use client';

import { useState } from 'react';
import { useRouter } from 'next/router';
import { Rocket } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { authAPI } from '@/utils/api';

export default function RegisterPage() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [grade, setGrade] = useState('');
  const [school, setSchool] = useState('');
  const [parentEmail, setParentEmail] = useState('');
  const [parentPhone, setParentPhone] = useState('');
  const [emirate, setEmirate] = useState('');
  const [state, setState] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleRegister = async (e) => {
    e.preventDefault();
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await authAPI.register({
        firstName,
        lastName,
        email,
        password,
        grade,
        school,
        parentEmail,
        parentPhone,
        country: 'United Arab Emirates',
        emirate,
        state,
      });

      const { token, user } = response.data;
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      router.push('/dashboard');
    } catch (error) {
      setError(error.response?.data?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const emiratesOptions = [
    'Abu Dhabi',
    'Dubai',
    'Sharjah',
    'Ajman',
    'Umm Al-Quwain',
    'Fujairah',
    'Ras Al Khaimah',
  ];

  return (
    <div className="min-h-screen bg-[#023148] flex flex-col items-center p-6">
      {/* Header */}
      <header className="w-full flex justify-between items-center mb-8">
        <Image src="/codesprint-logo.png" alt="CodeSprint Logo" width={180} height={60} />
        <Link href="/login">
          <button className="px-4 py-2 bg-[#FFC107] text-white font-bold rounded-lg hover:bg-[#FFB300] transition">
            Login
          </button>
        </Link>
      </header>

      {/* Main Section */}
      <div className="flex flex-col-reverse md:flex-row items-center gap-10 w-full max-w-5xl">
        {/* Mascot Section */}
        <div className="flex flex-col items-center">
          <Image src="/glitch-mascot.png" alt="Glitch Mascot" width={300} height={300} />
          <p className="text-lg font-semibold text-[#5E2C99] mt-4 animate-bounce">
            "Embark on your coding adventure!"
          </p>
          <p className="text-sm text-gray-600 text-center">
            "Learn. Code. Succeed. Your journey to tech brilliance starts here!"
          </p>
        </div>

        {/* Registration Form */}
        <div className="bg-white rounded-2xl shadow-lg p-6 border-4 border-[#5E2C99] max-w-lg w-full">
          <h1 className="text-3xl font-bold text-[#5E2C99] mb-4 text-center">Join CodeSprint</h1>
          <p className="text-sm text-gray-600 mb-6 text-center">
            Ready to join the coolest space crew in coding?
          </p>
          <form onSubmit={handleRegister} className="space-y-4">
            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#5E2C99]">First Name</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                  placeholder="First Name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#5E2C99]">Last Name</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                  placeholder="Last Name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                />
              </div>
            </div>

            {/* Other Fields */}
            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Email</label>
              <input
                type="email"
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                placeholder="Your Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 border-2 border-[#22ff7ee4] rounded-md"
                placeholder="Secret Code"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Confirm Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 border-2 border-[#22ff7ee4] rounded-md"
                placeholder="Confirm Code"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Grade</label>
              <select
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                value={grade}
                onChange={(e) => setGrade(e.target.value)}
                required
              >
                <option value="">Select Grade</option>
                {[...Array(10)].map((_, i) => (
                  <option key={i + 1} value={i + 1}>
                    Grade {i + 1}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">School</label>
              <input
                type="text"
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                placeholder="School Name"
                value={school}
                onChange={(e) => setSchool(e.target.value)}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Parent's Email</label>
              <input
                type="email"
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                placeholder="Parent's Email"
                value={parentEmail}
                onChange={(e) => setParentEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Parent's Phone</label>
              <input
                type="text"
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                placeholder="Parent's Phone"
                value={parentPhone}
                onChange={(e) => setParentPhone(e.target.value)}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Country</label>
              <input
                type="text"
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md bg-gray-100 text-gray-700"
                value="United Arab Emirates"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[#5E2C99]">Emirate</label>
              <select
                className="w-full px-3 py-2 border-2 border-[#FFC107] rounded-md"
                value={emirate}
                onChange={(e) => setEmirate(e.target.value)}
                required
              >
                <option value="">Select Emirate</option>
                {emiratesOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>
            {error && <p className="text-red-500 text-sm">{error}</p>}
            <button
              type="submit"
              disabled={loading}
              className="w-full py-3 bg-[#4CAF50] text-white font-bold rounded-lg hover:bg-[#45a049] transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Rocket className="inline-block mr-2 h-5 w-5" />
              {loading ? 'Joining the Crew...' : 'Join the Space Crew!'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
