/* General dashboard container */
.dashboardContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: 100vh;
  background-color: rgb(var(--background-start-rgb));
}

/* Card styling */
.card {
  padding: 2rem;
  border-radius: var(--border-radius);
  background: rgba(var(--card-rgb), 0.1);
  border: 1px solid rgba(var(--card-border-rgb), 0.3);
  transition: background 200ms, border 200ms;
  max-width: 800px;
  width: 100%;
  text-align: center;
  margin-bottom: 2rem;
}

/* Logo styling */
.logoContainer {
  margin-bottom: 1rem;
}

.logo {
  width: 100px;
  height: auto;
  margin: 0 auto;
}

/* Title styling */
.title {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Content container styling */
.contentContainer {
  text-align: left;
  margin-top: 2rem;
}

/* Levels container */
.levels {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .levels {
    grid-template-columns: 1fr 1fr;
  }
}

.levelCard {
  padding: 1.5rem;
  background-color: #333;
  color: #fff;
  border-radius: var(--border-radius);
}

.levelCard h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.levelCard ul {
  list-style-type: none;
  padding: 0;
}

/* Timeline container */
.timeline {
  margin-top: 3rem;
}

.timeline h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
}

.timelineItem {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #333;
  color: #fff;
  border-radius: var(--border-radius);
}

.timelineItem h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.timelineItem ul {
  list-style-type: none;
  padding: 0;
}

/* Logout button styling */
.logoutButton {
  background-color: #e53e3e;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 200ms;
  margin-top: 2rem;
}

.logoutButton:hover {
  background-color: #c53030;
}

/* Premium button styling */
.premiumButton {
  background-color: #0070f3;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 200ms;
  margin-top: 1.5rem;
}

.premiumButton:hover {
  background-color: #005bb5;
}
