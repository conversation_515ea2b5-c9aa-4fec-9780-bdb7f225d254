.container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
  }
  
  .header {
	background-color: #023148;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 40px;
  }
  
  .logo {
	flex-shrink: 0;
  }
  
  .nav {
	display: flex;
	gap: 30px;
  }
  
  .nav a {
	color: #FB8602;
	text-decoration: none;
	font-weight: bold;
	font-size: 1.2rem;
  }
  
  .loginButton {
	background-color: #229EBD;
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 5px;
	font-size: 1.2rem;
	font-weight: bold;
	cursor: pointer;
  }
  
  .main {
	background-color: #023148;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 60px 40px;
  }
  
  .content {
	max-width: 50%;
  }
  
  .title {
	font-size: 5rem;
	font-weight: bold;
	color: #FFB700;
	line-height: 1.1;
	margin-bottom: 30px;
  }
  
  .description {
	font-size: 1.2rem;
	color: #fff;
	margin-bottom: 40px;
	line-height: 1.6;
  }
  
  .registerButton {
	background-color: #FFB700;
	color: #023148;
	border: none;
	padding: 15px 30px;
	border-radius: 25px;
	font-size: 1.4rem;
	font-weight: bold;
	cursor: pointer;
  }
  
  .mascot {
	flex-shrink: 0;
  }
  
  .coursesSection {
	background-color: #229EBD;
	padding: 60px 40px;
  }
  
  .coursesHeader {
	display: flex;
	align-items: center;
	margin-bottom: 40px;
  }
  
  .coursesTitle {
	font-size: 5rem;
	font-weight: bold;
	color: #FFB700;
	margin-left: 20px;
  }
  
  .coursesContent {
	display: flex;
	gap: 20px;
	padding-right : 20px;
	padding-left: 20px;
  }
  
  .coursesIntro {
	flex: 0 0 40%;
	background-color: #FFB700;
	padding: 50px;
	border-radius: 10px;
	display: flex;
	flex-direction: column;
	gap: 20px;
  }
  
  .coursesIntro p {
	color: #023148;
	font-size: 1rem;
	line-height: 1.6;
  }
  
  .coursesGrid {
	flex: 0 0 60%;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20px;
  }
  
  .courseBox {
	aspect-ratio: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 10px;
  }
  
  .orangeBox {
	background-color: #FB8602;
  }
  
  .blueBox {
	background-color: #023148;
  }
  
  /* Adjust the grid for the last two boxes */
  .coursesGrid > div:nth-last-child(-n+2) {
	grid-column: span 1.5;
  }
  /* Add these new styles to your existing Home.module.css file */

.aboutSection {
	background-color: #023148;
	min-height: 100vh;
	position: relative;
	overflow: hidden;
  }
  
  .aboutTitle {
	font-size: 120px;
	font-weight: bold;
	color: transparent;
	-webkit-text-stroke: 2px #fff;
	letter-spacing: 4px;
  }
  
  .aboutContent {
	display: flex;
	align-items: center;
	gap: 2rem;
	margin-top: 1rem;
  }
  
  .aboutText {
	background-color: #FB8602;
	border-radius: 1.5rem;
	padding: 3rem;
	max-width: 48rem;
  }
  
  .aboutText p {
	color: white;
	font-size: 1.125rem;
	line-height: 1.75;
  }
  
  .aboutMascot {
	flex-shrink: 0;
  }