/* Center content using flexbox */
.loginContainer {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    min-height: 100vh; /* Full viewport height to ensure vertical centering */
    background-color: rgb(var(--background-start-rgb));
  }

  .logo {
    width: 150px; /* Adjust the size as needed */
    margin-bottom: 20px; /* Space between the logo and the form */
    vertical-align: middle;
    top: 50;
    left: 3;
    
  }
  
  /* Structure the card and form content */
  .card {
    padding: 2rem;
    border-radius: var(--border-radius);
    background: rgba(var(--card-rgb), 0.1);
    border: 1px solid rgba(var(--card-border-rgb), 0.3);
    transition: background 200ms, border 200ms;
    max-width: 400px;
    width: 100%;
    text-align: center; /* Center text inside the card */
  }
  
  /* Logo styling */
  .logoContainer {
    margin-bottom: 1rem;
  }
  
  .logo {
    color: #0070f3;
    width: 100px; /* Adjust the size of the logo */
    height: auto;
    margin: 0 auto; /* Center the logo */
  }
  
  /* Input field styling */
  .input {
    width: 100%;
    padding: 0.75rem;
    margin-top: 0.25rem;
    border: 1px solid rgba(var(--callout-border-rgb), 0.3);
    border-radius: var(--border-radius);
    background: rgba(var(--card-rgb), 0.1);
  }
  
  .input:focus {
    outline: none;
    border-color: rgba(var(--card-border-rgb), 0.6);
  }
  
  /* Error message styling */
  .error {
    color: red;
    margin-bottom: 1rem;
    font-size: 0.85rem;
  }
     
  /* Button styling */
  .btn {
    background-color: #0070f3;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 200ms;
    width: 100%;
    text-align: center;
  }
  
  .btn:hover {
    background-color: #005bb5;
  }
  
  /* Title styling */
  .title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  /* Description styling */
  .description {
    margin-top: 1rem;
  }
  /* Login.module.css */
.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #027381, #f35329);
}

.card {
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
}

.logoContainer {
  margin-bottom: 20px;
}

.logo {
  width: 60px;
}

.title {
  font-size: 24px;
  margin-bottom: 20px;
  color: #027381;
}

.formGroup {
  margin-bottom: 15px;
  text-align: left;
  color: #333;
  font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
}

.label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
}

.input:focus {
  outline: none;
  border-color: #027381;
  box-shadow: 0 0 8px rgba(2, 115, 129, 0.2);
}

.btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #f77751, #e46b32);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn:hover {
  background: linear-gradient(135deg, #e46b32, #f35329);
}

.error {
  color: #e46b32;
  margin-top: 10px;
}

.mascot {
	position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  }
  
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;
    margin-right: 50px;
    }

    .logo {
      flex-shrink: 0;
      }
      .nav {
        display: flex;
        gap: 20px;
        }
        
        .nav a {
        color: #fe7751;
        text-decoration: none;
        font-weight: bold;
        }
        
        .loginButton {
        background-color: #027381;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        }    

