import mongoose from 'mongoose';

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  password: {
    type: String,
    required: true,
  },
  isPremium: {
    type: Boolean,
    default: false,
  },
  premiumLevel: {
    type: String,  // Store the level the user has chosen
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5'],
    default: null,
  },
  pendingOrderId: {
    type: String,
    default: null,
  },
  pendingPremiumLevel: {
    type: String,
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5'],
    default: null,
  }
});

export default mongoose.models.User || mongoose.model('User', UserSchema);
