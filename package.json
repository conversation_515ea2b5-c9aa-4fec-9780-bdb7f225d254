{"name": "codesprint-monorepo", "version": "1.0.0", "description": "CodeSprint - Full Stack Learning Platform", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "rimraf node_modules backend/node_modules frontend/node_modules", "lint": "cd frontend && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "workspaces": ["backend", "frontend"], "keywords": ["education", "coding", "learning", "platform"], "author": "CodeSprint Team", "license": "ISC"}