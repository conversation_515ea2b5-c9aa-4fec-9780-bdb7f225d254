'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts'
import { Rocket, LogOut, Code, Brain, Zap } from 'lucide-react'
import Image from 'next/image'

const achievementsData = [
  { name: 'AI', value: 5 },
  { name: 'Coding', value: 10 },
  { name: 'Blockchain', value: 7 },
  { name: 'Quantum', value: 3 },
]

export default function Dashboard() {
  const router = useRouter()
  const [isPremium, setIsPremium] = useState(false)
  const [premiumLevel, setPremiumLevel] = useState('')
  const [loading, setLoading] = useState(true)
  const [userName, setUserName] = useState('')
  const [userGrade, setUserGrade] = useState('')

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
    } else {
      fetch('/api/verify-token', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`,
        },
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.isPremium) {
            setIsPremium(true)
            setPremiumLevel(data.premiumLevel)
          }
          setUserName(data.name)
          setUserGrade(data.grade)
          setLoading(false)
        })
        .catch(() => {
          localStorage.removeItem('token')
          router.push('/login')
        })
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    router.push('/login')
  }

  const handleSubscribe = () => {
    router.push('/go-premium')
  }

  const renderPremiumContent = () => {
    return (
      <div className="bg-[#003366] rounded-xl p-4 shadow-md text-white">
        <h2 className="text-xl font-bold mb-2 text-[#FFA500]">Premium Content: {premiumLevel}</h2>
        <ul className="list-disc pl-5 text-sm">
          {premiumLevel === 'Level 1' && (
            <>
              <li>Advanced AI techniques</li>
              <li>Blockchain fundamentals</li>
              <li>Quantum computing introduction</li>
              <li>Cybersecurity essentials</li>
            </>
          )}
          {premiumLevel === 'Level 2' && (
            <>
              <li>Machine Learning projects</li>
              <li>Smart contract development</li>
              <li>Quantum algorithms</li>
              <li>Ethical hacking workshops</li>
            </>
          )}
        </ul>
      </div>
    )
  }

  if (loading) {
    return <div className="flex items-center justify-center h-screen bg-[#001F3F]">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-[#001F3F] text-white p-6 relative">
      <header className="flex justify-between items-center mb-8">
        <Image src="/codesprint-logo.png" alt="CodeSprint Logo" width={150} height={50} />
        <button
          onClick={handleLogout}
          className="flex items-center bg-[#FFA500] text-[#001F3F] rounded-full px-4 py-2 text-sm font-bold hover:bg-[#FF8C00] transition-colors duration-200"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </button>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#003366] rounded-2xl p-6 shadow-md">
          <h2 className="text-3xl font-bold mb-4 text-[#FFA500]">WELCOME, {userName}!</h2>
          
          <div className="bg-[#00264D] rounded-xl p-4 mb-4">
            <h3 className="text-xl font-semibold mb-2 text-[#FFA500]">PROFILE</h3>
            <div className="flex items-center space-x-4">
              <Image src="/profile.png" alt="Profile" width={50} height={50} className="rounded-full" />
              <div>
                <p className="font-medium">{userName}</p>
                <p className="text-sm text-[#B0C4DE]">Grade: {userGrade}</p>
                <p className="text-sm text-[#B0C4DE]">Level: {isPremium ? premiumLevel : 'Free'}</p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-2 text-[#FFA500]">Achievements</h3>
            <BarChart width={300} height={200} data={achievementsData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#B0C4DE" />
              <XAxis dataKey="name" stroke="#B0C4DE" />
              <YAxis stroke="#B0C4DE" />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" fill="#FFA500" />
            </BarChart>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]">
            <h2 className="text-2xl font-bold mb-4 text-[#FFA500]">PROGRESS</h2>
            <div className="w-48 h-48 mx-auto">
              <div className="w-full h-full rounded-full border-8 border-[#FFA500] flex items-center justify-center">
                <div className="w-3/4 h-3/4 rounded-full bg-[#FFA500] flex items-center justify-center">
                  <Zap className="w-12 h-12 text-[#003366]" />
                </div>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <button
                onClick={() => {
                  if (isPremium) {
                    // Redirect to the appropriate lesson page based on premium level
                    router.push(`/lessons/${premiumLevel.toLowerCase().replace(' ', '-')}`)
                  } else {
                    alert('Upgrade to premium to access lessons');
                  }
                }}
                className="bg-[#0066CC] text-white rounded-xl p-6 text-xl font-bold hover:bg-[#005AB5] transition-colors duration-200 flex items-center justify-center"
              >
                <Code className="mr-2" />
                Lessons
            </button>
            <button className="bg-[#FFA500] text-[#001F3F] rounded-xl p-6 text-xl font-bold hover:bg-[#FF8C00] transition-colors duration-200 flex items-center justify-center">
              <Brain className="mr-2" />
              Quiz
            </button>
          </div>
        </div>
        <div className="bg-[#003366] rounded-2xl p-6 shadow-md">
          <h2 className="text-2xl font-bold mb-4 text-[#FFA500]">Daily Challenge</h2>
          <div className="flex items-end justify-between h-full">
            <div className="w-1/2"></div>
            <div className="w-1/2 text-center">
              <Image src="/IndexOfCourse-mascot.png" alt="Robot Mascot" width={150} height={150} className="mx-auto" />
              {isPremium ? (
                renderPremiumContent()
              ) : (
                <button
                  onClick={handleSubscribe}
                  className="bg-[#FFA500] text-[#001F3F] rounded-full px-6 py-2 text-xl font-bold mt-4 hover:bg-[#FF8C00] transition-colors duration-200"
                >
                  Go Premium
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {!isPremium && (
        <button
          onClick={handleSubscribe}
          className="fixed bottom-6 right-6 bg-[#FFA500] hover:bg-[#FF8C00] text-[#001F3F] rounded-full px-6 py-3 text-xl font-bold shadow-lg transition-colors duration-200 ease-in-out flex items-center"
        >
          <Rocket className="mr-2 h-5 w-5" />
          Subscribe
        </button>
      )}
    </div>
  )
}