"use client";
import React, { useState } from 'react'; // Only import React once at the top
import PropTypes from "prop-types"; // Import PropTypes for prop validation


// Modal Component
const Modal = ({ isOpen, onClose, children }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            {/* Background overlay */}
            <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>

            {/* Modal content */}
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 relative z-10 shadow-lg">
                <button onClick={onClose} className="absolute top-2 right-2 text-gray-600 hover:text-gray-800">
                    ✖️
                </button>
                {children}
            </div>
        </div>
    );
};

Modal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    children: PropTypes.node.isRequired,
  };
  

// IndexOfCourse Component
const IndexOfCourse = () => {
    return (
        <div className="p-4">
            <div className="mt-4">
                <h3 className="text-lg font-semibold">CONTENTS</h3>
                <ul className="list-disc list-inside">
                    <li>
                        <strong>01. HELLO WORLD!</strong>
                        <ul className="ml-4">
                            <li>PRINT(“PYTHON🐍”)</li>
                            <li>HELLO WORLD</li>
                            <li>PYRAMID</li>
                            <li>ASQUÍ ART</li>
                            <li>DEAR FUTURE ME,</li>
                        </ul>
                    </li>
                    <li>
                        <strong>02. DATA TYPES</strong>
                        <ul className="ml-4">
                            <li>VARIABLES</li>
                            <li>DATA TYPES</li>
                            <li>WHEW!! IT’S HOT OUT HERE</li>
                        </ul>
                    </li>
                    <li>
                        <strong>03. CONTROL FLOW</strong>
                        <ul className="ml-4">
                            <li>WEAK PASSWORD, TRY AGAIN</li>
                            <li>ARE YOU A CAT PERSON?</li>
                            <li>LIGHTS, CAMERA, ACTION!</li>
                            <li>BOOK OF ANSWERS</li>
                            <li>GOD OF THUNDER - THOR ODINSON</li>
                        </ul>
                    </li>
                    <li>
                        <strong>04. LOOPS AND HOOPS</strong>
                        <ul className="ml-4">
                            <li>DON’T MAKE ME GUESS</li>
                            <li>level 4 (7 - 8) 2</li>
                            <li>OH, YOU ARE ACTUALLY MAKING ME GUESS 😒</li>
                            <li>PLIER, ÉTENDRE, RELEVER, ÉLANCER</li>
                            <li>MY TRUE LOVE SENT TO ME,</li>
                        </ul>
                    </li>
                    <li>
                        <strong>05. FINISH LINE!</strong>
                    </li>
                </ul>
            </div>
        </div>
    );
};

// Main Component
export default function CourseContentModal() {
    const [isAmirModalOpen, setIsAmirModalOpen] = useState(false);

    const handleOpenAmirModal = () => setIsAmirModalOpen(true);
    const handleCloseAmirModal = () => setIsAmirModalOpen(false);

    return (
        <div className="max-w-3xl mx-auto p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Course Section</h1>
            
            <button
                onClick={handleOpenAmirModal}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
                Index of Course
            </button>

            {/* Modal to show IndexOfCourse component */}
            <Modal isOpen={isAmirModalOpen} onClose={handleCloseAmirModal}>
                <IndexOfCourse />
            </Modal>
        </div>
    );
}


/* export default function CourseIndex() {
    return (
        <div className="max-w-xl w-11/12 mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
            <h1 className="text-3xl font-semibold text-gray-800 mb-6">Course Index</h1>
            <ul className="space-y-4">
                <li className="bg-indigo-100 hover:bg-indigo-200 transition transform hover:-translate-y-1 rounded-lg p-4">
                    <div className="flex items-center font-semibold text-indigo-700 text-lg mb-2">
                        <span className="mr-2 text-2xl">📖</span>01. HELLO WORLD!
                    </div>
                    <ul className="pl-6 text-gray-700">
                        <li>PRINT(“PYTHON🐍”)</li>
                        <li>HELLO WORLD</li>
                        <li>PYRAMID</li>
                        <li>ASQUÍ ART</li>
                        <li>DEAR FUTURE ME,</li>
                    </ul>
                </li>
                <li className="bg-indigo-100 hover:bg-indigo-200 transition transform hover:-translate-y-1 rounded-lg p-4">
                    <div className="flex items-center font-semibold text-indigo-700 text-lg mb-2">
                        <span className="mr-2 text-2xl">📊</span>02. DATA TYPES
                    </div>
                    <ul className="pl-6 text-gray-700">
                        <li>VARIABLES</li>
                        <li>DATA TYPES</li>
                        <li>WHEW!! IT’S HOT OUT HERE</li>
                    </ul>
                </li>
                <li className="bg-indigo-100 hover:bg-indigo-200 transition transform hover:-translate-y-1 rounded-lg p-4">
                    <div className="flex items-center font-semibold text-indigo-700 text-lg mb-2">
                        <span className="mr-2 text-2xl">🛠️</span>03. CONTROL FLOW
                    </div>
                    <ul className="pl-6 text-gray-700">
                        <li>WEAK PASSWORD, TRY AGAIN</li>
                        <li>ARE YOU A CAT PERSON?</li>
                        <li>LIGHTS, CAMERA, ACTION!</li>
                        <li>BOOK OF ANSWERS</li>
                        <li>GOD OF THUNDER - THOR ODINSON</li>
                    </ul>
                </li>
                <li className="bg-indigo-100 hover:bg-indigo-200 transition transform hover:-translate-y-1 rounded-lg p-4">
                    <div className="flex items-center font-semibold text-indigo-700 text-lg mb-2">
                        <span className="mr-2 text-2xl">🔄</span>04. LOOPS AND HOOPS
                    </div>
                    <ul className="pl-6 text-gray-700">
                        <li>DON’T MAKE ME GUESS</li>
                        <li>level 4 (7 - 8) 2</li>
                        <li>OH, YOU ARE ACTUALLY MAKING ME GUESS 😒</li>
                        <li>PLIER, ÉTENDRE, RELEVER, ÉLANCER</li>
                        <li>MY TRUE LOVE SENT TO ME,</li>
                    </ul>
                </li>
                <li className="bg-indigo-100 hover:bg-indigo-200 transition transform hover:-translate-y-1 rounded-lg p-4">
                    <div className="flex items-center font-semibold text-indigo-700 text-lg">
                        <span className="mr-2 text-2xl">🏁</span>05. FINISH LINE!
                    </div>
                </li>
            </ul>
        </div>
    );
} */
