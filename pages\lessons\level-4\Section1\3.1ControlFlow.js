import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const WeakPassword = () => {
  const [code] = useState(`# Enter a password\n`);
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const [strongCount, setStrongCount] = useState(0);
  const [weakCount, setWeakCount] = useState(0);
  const outputRef = useRef(null);

  const typedText = useTypingAnimation("WEAK PASSWORD, TRY AGAIN", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


  const runCode = () => {
    try {

      const simulatedInput = userInput;
      let output = "";


      if (simulatedInput.length >= 8) {
        output = "Strong password";
        setStrongCount((prev) => prev + 1);
      } else {
        output = "Weak password";
        setWeakCount((prev) => prev + 1);
      }


      setOutput(`${output}`);
      setShowOutput(true);
      setShowConfetti(simulatedInput.length >= 8);


      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);

      setTimeout(() => {
        document
          .querySelector(`.${styles.confetti}`)
          ?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
    } catch (error) {
      console.error(error);
      setOutput("Error: Incorrect Code! Please try again.");
      setShowOutput(true);
    }
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {

      const inputWithoutPrompt = newCode.replace(/^# Enter a password\n/, "");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #d1105a, #f24b04)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #d1105a, #f24b04)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700">
          So far, every Python program we&apos;ve seen has followed a single path of
          execution — they all run one line at a time, from top to bottom,
          always producing the same result each time you run them. But
          sometimes, we want our programs to do different things based on
          different conditions.
        </p>
        <p className="text-lg text-gray-700 mt-4">
          Imagine you&apos;re at a crossroad, and the sign ahead says &quot;Beach: left.
          Mountains: right.&quot; This tells us that if we want to go to the beach,
          we have to take the left, else if we want to go to the mountains, we
          take the right.
        </p>
        <p className="text-lg text-gray-700 mt-4">
          In this chapter, we&apos;ll learn how programs &quot;make decisions&quot; by
          evaluating different conditions. This is called &quot;Control Flow.&quot; Let&apos;s
          explore it with a fun example.
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
        <p className="mt-4 text-gray-700">
          Before we dive deep into the{" "}
          <code className="text-[#87403E]">if</code> condition, let&apos;s try
          something fun. Create a{" "}
          <code className="text-[#87403E]">password.py</code> program and type
          in this code:
        </p>
        <div className="bg-gray-100 p-4 rounded-lg text-left mt-4">
          <pre className="text-sm text-[#3B4555]">
            {`password = input("Enter a password: ")
if len(password) >= 8:
    print('Strong password')
else:
    print('Weak password')`}
          </pre>
        </div>
        <p className="mt-4 text-gray-700">
          This program checks the length of the password entered by the user:
        </p>
        <ul className="text-sm text-[#3B4555] list-disc pl-6">
          <li>
            If the password is 8 characters or longer, it prints &quot;Strong
            password&quot;.
          </li>
          <li>
            If the password is shorter than 8 characters, it prints &quot;Weak
            password&quot;.
          </li>
        </ul>
        <p className="mt-4 text-gray-700">
          Run the program a few times with different passwords to see the
          if/else statement in action!
        </p>
        <p className="mt-4 text-gray-700">
          How many times did you get &quot;Strong password&quot;? ✨
        </p>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#7d8a87",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#9caea9",
              color: "#6f1a07",
              minHeight: "150px",
              borderRadius: "10px",
            }}
          />
        </div>
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "74%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
             )} 
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#326273",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#D0F0F0",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#D0F0F0", marginTop: "10px" }}
              >
                {output}
              </pre>
              <div
                style={{
                  backgroundColor: "#326273",
                  padding: "15px",
                  borderRadius: "10px",
                  textAlign: "left",
                  marginTop: "20px",
                  border: "3px solid #D0F0F0",
                }}
              >
                <pre style={{ color: "#D0F0F0" }}>
                  You have entered{" "}
                  <strong style={{ fontSize: "1rem", color: "#74c69d" }}>
                    {strongCount}
                  </strong>{" "}
                  Strong password(s)
                </pre>
                <pre style={{ color: "#D0F0F0" }}>
                  You have entered{" "}
                  <strong style={{ fontSize: "1rem", color: "#ff4d6d" }}>
                    {weakCount}
                  </strong>{" "}
                  Weak password(s)
                </pre>
              </div>
            </div>
          )}
          <button
            onClick={runCode}
            className={styles.fadeInUp}
            style={{
              position: "relative",
              zIndex: 10,
              marginTop: "20px",
              backgroundColor: "#326273",
              color: "#D0F0F0",
              padding: "10px 20px",
              borderRadius: "30px",
              fontWeight: "bold",
              fontSize: "1rem",
              cursor: "pointer",
              boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
            }}
          >
            Run Code 🎉
          </button>
        </div>
      </div>
    </div>
  );
};

export default WeakPassword;
