import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const TheGoodTheBadAndTheElif = () => {
  const [code] = useState(
    `what genre do you prefer?(action, drama, comedy, etc..)\n`
  );
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);
  const [buttonClicked, setButtonClicked] = useState(false);
  const typedText = useTypingAnimation("The Good, The Bad, and The Elif", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


const runCode = () => {
  try {
    const simulatedInput = userInput?.trim().toLowerCase();

    if (!simulatedInput) {
      setOutput("Error: Please provide an input.");
      setShowOutput(true);
      scrollToOutput();
      return;
    }

    let output = "";
    let confetti = false;

    switch (simulatedInput) {
      case "action":
        output = "You should watch Avengers: End Game.";
        confetti = true;
        break;
      case "comedy":
        output = "You should watch [your favorite comedy movie name].";
        confetti = true;
        break;
      case "drama":
        output = "You should watch [your favorite drama movie name].";
        confetti = true;
        break;
      case "horror":
        output = "You should watch [your favorite horror movie name].";
        confetti = true;
        break;
      default:
        output = "That's an interesting choice! Maybe try a documentary.";
        confetti = false;
        break;
    }

    setOutput(output);
    setShowConfetti(confetti);
    setShowOutput(true);
    scrollToOutput();


    if (confetti) {
      setTimeout(() => setShowConfetti(false), 1000);
    }
  } catch {
    setOutput("Error: An unexpected error occurred.");
    setShowOutput(true);
  }
};


const scrollToOutput = () => {
  setTimeout(() => {
    if (outputRef.current) {
      outputRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, 100);
};
















  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {

      const lines = newCode.split("\n");
      const prompt = "what genre do you prefer?(action, drama, comedy, etc..)";
      console.log(prompt);
      const inputWithoutPrompt = lines.slice(1).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #92b4a7, #81667a)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #92b4a7, #81667a)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700 mb-6">
          Learn about relational operators and how to use{" "}
          <code className="font-mono text-blue-500">if</code>,{" "}
          <code className="font-mono text-blue-500">elif</code>, and{" "}
          <code className="font-mono text-blue-500">else</code> statements for
          decision-making.
        </p>
        <div
          className="bg-gray-100 p-6 rounded-lg text-left"
          style={{ textAlign: "center" }}
        >
          <h2 className="text-xl font-bold text-gray-800">
            Relational Operators
          </h2>
          <p className="text-gray-700 mt-2">
            Often inside conditions, we need to compare two values. For this, we
            use relational operators, which help us compare values:
            <ul
              className="list-disc pl-6 mt-2 text-gray-700"
              style={{ textAlign: "start" }}
            >
              <li>
                <code className="font-mono text-blue-500">==</code> equal to
              </li>
              <li>
                <code className="font-mono text-blue-500">!=</code> not equal to
              </li>
              <li>
                <code className="font-mono text-blue-500">&gt;</code> greater
                than
              </li>
              <li>
                <code className="font-mono text-blue-500">&lt;</code> less than
              </li>
              <li>
                <code className="font-mono text-blue-500">&gt;=</code> greater
                than or equal to
              </li>
              <li>
                <code className="font-mono text-blue-500">&lt;=</code> less than
                or equal to
              </li>
            </ul>
          </p>

          <h2 className="text-xl font-bold text-gray-800 mt-6">Elif</h2>
          <p className="text-gray-700 mt-2">
            Sometimes two conditions just aren&apos;t enough. That&apos;s where{" "}
            <code className="font-mono text-blue-500">elif</code> (short for &quot;else
            if&quot;) comes in. You can add one or more{" "}
            <code className="font-mono text-blue-500">elif</code> statements between
            your <code className="font-mono text-blue-500">if</code> and{" "}
            <code className="font-mono text-blue-500">else</code> to check additional
            conditions.
          </p>
          <p className="text-gray-700 mt-2">Let&apos;s make our programs even smarter! 🧠</p>


          <pre className="bg-gray-200 p-4 rounded-lg text-left text-gray-800 mt-4">
              {`light_color = 'yellow'
          if light_color == 'red':
              print('Stop')
          elif light_color == 'yellow':
              print('Slow down')
          elif light_color == 'green':
              print('Go')
          else:
              print('Invalid color')`}
            </pre>

            <p className="text-gray-700 mt-2">
              <strong>Note:</strong> Only one of these options will execute.
            </p>
        </div>
        <hr className="my-6 border-t border-gray-300" />
        <p className="mt-6 text-lg font-bold text-black">INSTRUCTIONS</p>
        <p className="text-sm text-gray-700 mt-2">
          We all love a good movie time with friends and family. Create a{" "}
          <code className="font-mono text-blue-500">movie_genre.py</code> program that
          suggests a movie genre based on user preference.
        </p>
        <p className="text-sm text-gray-700 mt-2">
          First, create a variable called{" "}
          <code className="font-mono text-blue-500">preference</code> and ask the user
          to input their desired genre (action, comedy, drama, or horror).
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800 mt-4">
          {`preference = input("What genre do you prefer? (action, drama, comedy, etc..)")`}
        </pre>
        <p className="text-sm text-gray-700 mt-2">
          Write an <code className="font-mono text-blue-500">if</code>,{" "}
          <code className="font-mono text-blue-500">elif</code>,{" "}
          <code className="font-mono text-blue-500">else</code> statement that:
        </p>
        <ul className="list-disc list-inside text-md pl-6 mt-2 text-gray-700">
          <li>
            <span className="font-mono text-blue-500">If</span> the preference is{" "}
            <span className="italic">&quot;action&quot;</span>,{" "}
            <span className="underline">output</span> &quot;You should watch Avengers:
            End Game&quot;.
          </li>
          <li>
            <span className="font-mono text-blue-500">If</span> the preference is{" "}
            <span className="italic">&quot;comedy&quot;</span>,{" "}
            <span className="underline">output</span> &quot;You should watch a [your
            favorite comedy movie name]&quot;.
          </li>
          <li>
            <span className="font-mono text-blue-500">If</span> the preference is{" "}
            <span className="italic">&quot;drama&quot;</span>,{" "}
            <span className="underline">output</span> &quot;You should watch an [your
            favorite drama movie name]&quot;.
          </li>
          <li>
            <span className="font-mono text-blue-500">If</span> the preference is{" "}
            <span className="italic">&quot;horror&quot;</span>,{" "}
            <span className="underline">output</span> &quot;You should watch a [your
            favorite horror movie name]&quot;.
          </li>
          <li>
            <span className="font-mono text-blue-500">Else</span>,{" "}
            <span className="underline">output</span> &quot;That&apos;s an interesting
            choice! Maybe try a documentary.&quot;
          </li>
        </ul>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#d0e1b3",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#e9f5db",
              color: "#d4a373",
              minHeight: "150px",
              borderRadius: "10px",
            }}
          />
        </div>{" "}
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "85%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#432818",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#CCD5AE",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#CCD5AE", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={() => {
                if (buttonClicked) {

                  setButtonClicked(false);
                  setUserInput("");
                  setOutput("");
                  setShowOutput(false);
                  setShowConfetti(false);
                  setAnimatedCode("");
                  setIsAnimationCompleted(false);
                } else {

                  runCode();
                  setButtonClicked(true);
                }
              }}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: buttonClicked ? "#ccd5ae" : "#432818",
                color: buttonClicked ? "#432818" : "#ccd5ae",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
                transition: "all 0.3s ease-in-out",
              }}
            >
              {buttonClicked ? "Retry 🔄" : "Run Code 🎉"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TheGoodTheBadAndTheElif;
