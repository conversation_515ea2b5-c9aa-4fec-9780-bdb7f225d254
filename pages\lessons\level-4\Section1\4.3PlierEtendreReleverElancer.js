import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const PlierEtendreReleverElancer = () => {
  const [code, setCode] = useState(`# Your Python code here
for i in range(40):
    print("5, 6, 7, 8!")
  `);
  const [output, setOutput] = useState("");
  const [showConfetti, setShow<PERSON>onfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);

  const outputRef = useRef(null);

  const typedText = useTypingAnimation("Fold, Extend, Lift, Propel.", 100);

  const [animatedCode, setAnimatedCode] = useState("");
  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


  const runCode = () => {
    try {
      const expectedCode = `# Your Python code here
for i in range(40):
    print("5, 6, 7, 8!")
      `.trim();

      if (code.trim() === expectedCode) {
        setOutput(`5, 6, 7, 8!\n`.repeat(40).trim());
        setShowOutput(true);
        setShowConfetti(true);
      } else {
        setOutput("Error: Please Correct The Code!");
        setShowOutput(true);
        setShowConfetti(false);
      }

      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);

      setTimeout(() => {
        document
          .querySelector(`.${styles.confetti}`)
          ?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
    } catch (error) {
      console.error(error);
      setOutput("Error: Incorrect Code! Please try again.");
      setShowOutput(true);
    }
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      setCode(newCode);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #F0EDCC, #02343F)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #F0EDCC, #02343F)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <div className="max-w-2xl text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">RANGE()</h2>
          <p className="text-lg text-gray-700">
            When you want to repeat a block of code a certain number of times,
            you can use a <code className="bg-gray-200 px-1 rounded">for</code>{" "}
            loop with the{" "}
            <code className="bg-gray-200 px-1 rounded">range()</code> function.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            The <code className="bg-gray-200 px-1 rounded">range()</code>{" "}
            function generates a sequence of numbers. By default, it starts at
            0, increments by 1, and stops just before the specified number.
          </p>
          <div className="bg-gray-100 p-4 rounded-lg text-left mt-4">
            <pre className="text-sm text-[#3B4555]">
              {`# Starting from 3 and going up to 8
for i in range(3, 9):
  print(i)`}
            </pre>
          </div>
          <p className="text-lg text-gray-700 mt-4">This will output:</p>
          <div className="bg-gray-100 p-4 rounded-lg text-left">
            <pre className="text-sm text-[#3B4555]">
              {`3
4
5
6
7
8`}
            </pre>
          </div>
          <p className="text-lg text-gray-700 mt-4">
            The loop starts at <strong>3</strong> and goes up to, but doesn&apos;t
            include, <strong>9</strong>.
          </p>
          <hr className="my-6 border-t border-gray-300" />
          <p className="text-lg font-bold text-black mb-4 mt-4">INSTRUCTIONS</p>
          <p className="text-lg text-gray-700">
            You&apos;re a dancer rehearsing for a big show, and your coach wants you
            to practice saying <strong>&quot;5, 6, 7, 8!&quot;</strong> 40 times before
            the final performance.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            Create a <code>dance_practice.py</code> program that repeats this
            phrase 40 times.
          </p>
          <p className="text-lg text-gray-700 mt-2">
            Use the code editor below to write your Python code and click &quot;Run
            Code&quot; to simulate the output.
          </p>
        </div>{" "}
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#8DAF8E",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#ACC7B4",
              color: "#331B3F",
              minHeight: "120px",
              borderRadius: "10px",
            }}
          />
        </div>
        <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#50586C",
                color: "#DCE2F0",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "48%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#50586C",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#DCE2F0",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#DCE2F0", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlierEtendreReleverElancer;
