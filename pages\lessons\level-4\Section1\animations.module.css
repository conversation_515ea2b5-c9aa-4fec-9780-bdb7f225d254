/* animations.module.css */

/* Define the fade-in-up animation */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px); /* Start from below */
  }
  100% {
    opacity: 1;
    transform: translateY(0); /* End at normal position */
  }
}

/* Confetti class with the animation */
.confetti {
  opacity: 0; /* Start with hidden confetti */
  animation: fadeInUp 0.5s ease-out forwards; /* Apply the fade-in-up animation */
}

.hidden {
  opacity: 0; /* Fade out */
}

