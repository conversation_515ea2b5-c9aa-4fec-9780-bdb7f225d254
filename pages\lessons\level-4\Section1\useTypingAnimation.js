
import { useState, useEffect } from 'react';

const useTypingAnimation = (code, typingSpeed = 100) => {
  const [animatedCode, setAnimatedCode] = useState('');
  
  useEffect(() => {
    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          if (nextChar !== undefined && nextChar !== null) {
            setAnimatedCode((prev) => prev + nextChar); 
          }
          currentIndex++;
        } else {
          clearInterval(typingInterval); 
        }
      }, typingSpeed);
    };

    typeCode();

    return () => clearInterval(typingInterval); 
  }, [code, typingSpeed]); 

  return animatedCode;
};

export default useTypingAnimation;
