import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const AQuicktour = () => {
  const typedText = useTypingAnimation("Popular Esports Games Overview", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Popular Esports Games</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Esports features a wide array of games, each offering a unique style
          of competition. Let&apos;s take a whirlwind tour through some of the most
          popular esports titles:
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Rocket League:</strong> Imagine soccer, but with
          rocket-powered cars! Players zoom around the field, attempting to
          score by hitting a giant ball into their opponent&apos;s goal. It&apos;s a
          high-speed, stunt-filled frenzy.
        </p>
        <img src="/rocket-league.png" alt="Rocket League" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Fortnite:</strong> A battle royale game where players fight to
          be the last one standing on a vast island. You can build structures,
          gather weapons, and outmaneuver opponents in inventive ways.
        </p>
        <img src="/fortnite.png" alt="Fortnite" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>League of Legends:</strong> This strategic game pits teams
          against each other in a quest to destroy the enemy&apos;s base while
          protecting their own. It&apos;s a dynamic mix of action and tactical
          planning, akin to a digital chess match.
        </p>
        <img
          src="/league-of-legends.png"
          alt="League of Legends"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Overwatch:</strong> A team-based game where players choose
          heroes with unique abilities and work together to achieve objectives.
          Success depends on teamwork and mastering your hero&apos;s role.
        </p>
        <img src="/overwatch.png" alt="Overwatch" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Super Smash Bros.:</strong> A lively fighting game featuring
          characters from various games, like Mario and Pikachu, battling it out
          in vibrant arenas. The goal is to knock your opponent off the stage
          using a combination of punches, kicks, and special moves.
        </p>
        <img
          src="/super-smash-bros.png"
          alt="Super Smash Bros."
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          These are just a few examples, but the world of esports is vast, with
          games to suit every taste. Whether you enjoy sports, strategy, or
          action, there&apos;s something for everyone in esports!
        </p>
      </div>
    </div>
  );
};

export default AQuicktour;
