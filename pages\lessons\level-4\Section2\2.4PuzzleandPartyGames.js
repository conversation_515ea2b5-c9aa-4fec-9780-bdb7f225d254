import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Actiongames = () => {
  const typedText = useTypingAnimation("Puzzle and Party Games", 100);

  return ( 
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          Not all esports games are about intense battles or deep strategy—some
          are just plain fun! Puzzle and party games are all about having a good
          time, whether you&apos;re solving tricky puzzles or playing wacky
          mini-games with friends.
        </p>
        <img
          src="/puzzle-games.png"
          alt="Puzzle and Party Games"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Tetris:</strong> In Tetris, you have to arrange falling blocks
          to create complete lines, which then disappear. The game speeds up as
          you play, so you need quick thinking and fast fingers to keep up.
          Tetris has been around for decades, and it&apos;s still a favorite for
          competitive play, with players trying to get the highest score
          possible.
        </p>
        <img src="/tetris.png" alt="Tetris" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Mario Party:</strong> Mario Party is like a virtual board
          game, where you roll dice to move around the board and play mini-games
          to earn coins and stars. The mini-games are where the real fun
          happens, with challenges like racing, jumping, and even fishing! Mario
          Party is great for playing with friends and family, and it&apos;s always
          full of surprises.
        </p>
        <img src="/mario-party.png" alt="Mario Party" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Other Fun Games:</strong> There are lots of other puzzle and
          party games that have competitive scenes, like{" "}
          <strong>Puyo Puyo</strong> (a puzzle game similar to Tetris) and{" "}
          <strong>Fall Guys</strong> (a party game where you race through
          obstacle courses). These games are all about having a blast, whether
          you&apos;re playing for fun or competing to be the best.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Puzzle and party games are perfect for players who enjoy light-hearted
          fun and creative challenges. They&apos;re great for playing with friends
          and are a reminder that esports is all about having a good time!
        </p>
      </div>
    </div>
  );
};

export default Actiongames;
