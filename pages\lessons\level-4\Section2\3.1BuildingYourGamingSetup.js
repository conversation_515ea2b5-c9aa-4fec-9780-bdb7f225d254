import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const BuildingYourGamingSetup = () => {
  const typedText = useTypingAnimation("Setting Up for Your Esports Journey", 100);

  return ( 
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <h2 className="text-2xl font-bold text-gray-800 mt-6">Building Your Gaming Setup</h2>
<p className="text-lg text-gray-700 mb-4">
  Before you can jump into the world of esports, you need the right tools! Here&apos;s a checklist of what you&apos;ll need to get started:
</p>
<ol className="list-decimal list-inside text-lg text-gray-700 mb-4">
  <li><strong>Gaming Console or PC:</strong> The backbone of your setup! Whether you&apos;re playing on a console like a PlayStation, Xbox, or a gaming PC, make sure your system meets the game&apos;s requirements.</li>
  <li><strong>Reliable Internet Connection:</strong> In esports, a laggy connection could be the difference between victory and defeat. Aim for a high-speed, stable internet connection.</li>
  <li><strong>Comfortable Gaming Chair:</strong> You&apos;ll spend a lot of time sitting while gaming, so a comfortable chair is essential to keep you focused and your posture healthy.</li>
  <li><strong>Headset with Microphone:</strong> Communication is key in team-based games, so a good headset with a clear microphone is a must.</li>
  <li><strong>Controller or Keyboard & Mouse:</strong> Depending on your game, you&apos;ll need a responsive controller or a high-quality keyboard and mouse for precision control.</li>
</ol>
<hr className="my-6 border-t border-gray-300" />
<h2 className="text-2xl font-bold text-gray-800 mt-6">Finding Your Esports Game</h2>
<p className="text-lg text-gray-700 mb-4">
  With so many games out there, how do you choose the one that&apos;s right for you? Consider what type of gameplay you enjoy the most:
</p>
<ul className="list-disc list-inside text-lg text-gray-700 mb-4">
  <li><strong>Do you love sports and competition?</strong> Try sports games like FIFA or NBA 2K.</li>
  <li><strong>Enjoy strategic thinking and planning?</strong> Strategy games like StarCraft or Chess might be your best bet.</li>
  <li><strong>Crave action-packed adventures?</strong> Dive into action games like Valorant or Apex Legends.</li>
</ul>
<p className="text-lg text-gray-700 mb-4">
  Try different games to see what clicks with you. And remember, it&apos;s okay to be a fan of more than one genre!
</p>
<hr className="my-6 border-t border-gray-300" />
<h2 className="text-2xl font-bold text-gray-800 mt-6">Joining the Community</h2>
<p className="text-lg text-gray-700 mb-4">
  Esports is all about community. Here&apos;s how to get involved:
</p>
<ul className="list-disc list-inside text-lg text-gray-700 mb-4">
  <li><strong>Join Online Forums and Groups:</strong> Sites like Reddit, Discord, and game-specific forums are great places to connect with fellow gamers, share tips, and find teammates.</li>
  <li><strong>Participate in Tournaments:</strong> Many games have local or online tournaments that are perfect for beginners. It&apos;s a great way to test your skills and meet other players.</li>
  <li><strong>Watch Professional Esports:</strong> Learn from the pros by watching esports events online. You&apos;ll pick up strategies, tips, and get a feel for how top players compete.</li>
</ul>
<p className="text-lg text-gray-700 mb-4">
  By engaging with the community, you&apos;ll improve your skills and make new friends who share your passion for gaming.
</p>
      </div>
    </div>
  );
};

export default BuildingYourGamingSetup;
