import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("THE WANNABE SCIENTIST", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>
            Changing the Outcome: How Wording Affects AI Responses
          </strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          The way you phrase your prompt can significantly impact the AI&apos;s
          response. Even a slight change in wording can lead to different
          results.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example:</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Prompt 1:</strong> &quot;What are the benefits of exercise?&quot;
          </li>
          <li>
            <strong>Prompt 2:</strong> &quot;Why is exercise important for mental
            health?&quot;
          </li>
          <li>
            Both prompts are about exercise, but the second one focuses on
            mental health, prompting a more specific response.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS:</strong> Take a basic prompt and reword it to
          see how the AI&apos;s response changes. For example:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Original:</strong> &quot;What are the uses of water?&quot;
          </li>
          <li>
            <strong>Variation:</strong> &quot;How is water utilized across different
            industries?&quot;
          </li>
        </ul>
        ---
        <p className="text-lg text-gray-700 mb-4">
          <strong>
            3.2 Fun with Variations: Tweaking Prompts for Different Results
          </strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Once you have a prompt, try modifying a word or two to see what
          changes. This is a great way to explore various aspects of a topic.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example:</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Original:</strong> &quot;Tell me about the life of a butterfly.&quot;
          </li>
          <li>
            <strong>Variation:</strong> &quot;Describe the journey of a caterpillar
            transforming into a butterfly.&quot;
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS:</strong> Choose a topic you&apos;re interested in and
          write three different prompts about it. Compare the AI&apos;s responses to
          see how your changes influenced the outcomes.
        </p>
        ---
        <p className="text-lg text-gray-700 mb-4">
          <strong>3.2 Prompt Challenges: Test Your Skills</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Now that you&apos;re familiar with prompt writing, let&apos;s put your skills to
          the test! Can you create a prompt that gets the AI to:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>Invent a new superhero with unique abilities.</li>
          <li>Describe a typical day for a student in the year 2050.</li>
          <li>
            Explain a complex concept (like gravity) in a way that a young child
            would understand.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS:</strong> Work with a partner and take turns
          writing prompts for each other. See who can come up with the most
          intriguing or creative AI responses!
        </p>
      </div>
    </div>
  );
};

export default Prompt;
