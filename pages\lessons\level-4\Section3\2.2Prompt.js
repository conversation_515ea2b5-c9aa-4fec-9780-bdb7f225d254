import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("LET'S GET A LIL' CREATIVE OVA HERE!", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Story Starters: Using AI to Write Stories</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Prompts can be a fantastic way to ignite a story. Whether you&apos;re stuck
          for ideas or just looking for a fresh start, a well-crafted prompt can
          inspire your imagination.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompts:</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            &quot;A mysterious box appears on your doorstep with no sender. What
            happens next?&quot;
          </li>
          <li>
            &quot;You wake up one day with the ability to understand animals. What do
            they say?&quot;
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Write your own story starter prompt and let the AI continue the story.
          Use the AI’s ideas to create your own unique narrative.
        </p>
        <hr className="my-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>
            Problem-Solving Prompts: Get AI to Help with Homework
          </strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          AI can assist with schoolwork by answering questions or clarifying
          concepts. A well-written prompt can help you grasp a difficult subject
          or solve a challenging problem.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompt:</strong> &quot;Explain how photosynthesis occurs in
          plants.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Think of a subject you find tough and write a prompt that could help
          you learn more about it. Use the AI&apos;s response to enhance your
          understanding.
        </p>
        <hr className="my-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Creating Games and Quizzes: Making Learning Fun</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Why not use prompts to create your own games or quizzes? Challenge
          your friends or even design a fun learning tool for yourself.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example Prompt:</strong> &quot;Create a quiz with 10 questions
          about medieval history.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Write prompts to develop a quiz or game. Test it out with your
          classmates and see who scores the highest!
        </p>
        <hr className="my-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Designing Your Own Prompts: Be the AI Master</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Now that you’ve learned the basics, it’s time to start crafting your
          own prompts. The more you practice, the better you’ll get at designing
          prompts that elicit interesting and useful AI responses.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Spend some time designing your own set of prompts on a topic that
          interests you. Experiment with different types of prompts and observe
          how the AI responds. Share your best prompts with the class and
          discuss what you’ve learned.
        </p>
      </div>
    </div>
  );
};

export default Prompt;
