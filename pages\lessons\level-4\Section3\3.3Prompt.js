import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("Key Terms and Definitions", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Understanding Important Terms</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          As you&apos;ve explored prompt engineering, you&apos;ve encountered several key
          terms and concepts. This glossary will help you remember what they
          mean.
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Prompt:</strong> A question or instruction given to an AI to
            generate a response.
          </li>
          <li>
            <strong>Specificity:</strong> The quality of being clear and
            detailed in a prompt.
          </li>
          <li>
            <strong>Clarity:</strong> The quality of being easy to understand in
            a prompt.
          </li>
          <li>
            <strong>AI (Artificial Intelligence):</strong> A technology that
            simulates human intelligence, capable of learning, reasoning, and
            problem-solving.
          </li>
          <li>
            <strong>Multi-Step Prompt:</strong> A series of prompts that guide
            the AI through a complex topic.
          </li>
        </ul>
        <hr className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Add any other terms you&apos;ve learned to this glossary. You can also
          write simple definitions in your own words to help you remember them.
        </p>
      </div>
    </div>
  );
};

export default Prompt;
