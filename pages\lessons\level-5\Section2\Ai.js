import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Sportsgames = () => {
  const typedText = useTypingAnimation("Artificial Intelligence", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Artificial Intelligence (AI)</strong> is one of the most
          fascinating technologies shaping our world today. It's all about
          making machines smart enough to understand, learn, and even make
          decisions on their own. AI powers the virtual assistants you talk to,
          the recommendation systems that suggest new songs or shows, and even
          self-driving cars that can navigate roads without a human driver. But
          how do these machines get so smart? The answer lies in two key
          concepts: Machine Learning (ML) and Neural Networks.{" "}
        </p>
        <img src="/ai.png" alt="FIFA" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>What is Artificial Intelligence ? :</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          At its core, AI is the simulation of human intelligence in machines.
          This means that computers can perform tasks that would normally
          require human intelligence, such as recognizing faces, understanding
          speech, or making decisions. AI is becoming a part of everyday life,
          even if you don't always notice it. Whether it's a chatbot helping you
          with customer service, or your phone predicting the words you're about
          to type, AI is at work behind the scenes.{" "}
        </p>
      </div>
    </div>
  );
};

export default Sportsgames;
