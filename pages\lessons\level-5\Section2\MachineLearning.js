import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Sportsgames = () => {
  
  const typedText = useTypingAnimation("Machine Learning", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>What is Machine Learning?</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Machine Learning is a special branch of AI that focuses on teaching
          computers to learn from data. Imagine you're teaching a computer to
          recognize different types of fruit. Instead of programming every
          single detail about what makes an apple different from an orange, you
          would show the computer thousands of pictures of apples and oranges.
          Over time, the computer learns to recognize each fruit by finding
          patterns in the data. This process of learning from examples and
          making predictions is what Machine Learning is all about.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Machine Learning is incredibly powerful because it allows computers to
          improve over time without needing to be reprogrammed. As the computer
          is exposed to more data, it becomes better at making accurate
          predictions, just like how you get better at something the more you
          practice.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Supervised Learning</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          In Supervised Learning, the computer is given a set of labeled data,
          which means it knows the correct answers. For example, if you were
          teaching a computer to recognize handwritten numbers, you would give
          it thousands of images of numbers, each labeled with the correct digit
          (like “3” or “7”). The computer uses these examples to learn and
          eventually gets good at identifying the numbers on its own.
        </p>
        <img
          src="/supervised-learning.png"
          alt="Supervised Learning"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Unsupervised Learning</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          In Unsupervised Learning, the computer is given data that isn't
          labeled. This means it has to figure out the patterns on its own.
          Imagine sorting a pile of mixed-up buttons by color and size without
          any instructions. The computer does something similar in Unsupervised
          Learning—it looks at the data, finds similarities, and groups things
          together based on those patterns.
        </p>
        <img
          src="/unsupervised-learning.png"
          alt="Unsupervised Learning"
          className="mb-4"
        />
      </div>
    </div>
  );
};

export default Sportsgames;
