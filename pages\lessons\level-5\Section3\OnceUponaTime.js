import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const GodOfThunder = () => {
  const [code, setCode] = useState("Enter a name:\n");
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [animatedCode, setAnimatedCode] = useState("");
  const [isRetry, setIsRetry] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [showOutput, setShowOutput] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [name, setName] = useState("");
  const [place, setPlace] = useState("");
  const [activity, setActivity] = useState("");
  const outputRef = useRef(null);

  const typedText = useTypingAnimation("Once Upon a Time", 100);

  const codeBlockStyle = {
    backgroundColor: "#f7fafc", // Light gray background
    padding: "16px", // Padding for space around the code
    borderRadius: "8px", // Rounded corners
    color: "#2d3748", // Text color
    marginTop: "16px", // Top margin for spacing
    whiteSpace: "pre-wrap", // Preserve spaces and newlines
    wordWrap: "break-word", // Allow long words to break into the next line
    fontFamily: "monospace", // Monospace font for code
    fontSize: "16px", // Slightly larger font size for readability
  };

  const goToNextStep = () => {
    if (userInput.trim() !== "") {
      runCode();
    }
  };

  const handleTyping = (newCode) => {
    const lines = newCode.split("\n");
    if (currentStep === 1) {
      const nameInput = lines[1]?.trim() || "";
      setUserInput(nameInput);
    } else if (currentStep === 2) {
      const placeInput = lines[1]?.trim() || "";
      setUserInput(placeInput);
    } else if (currentStep === 3) {
      const activityInput = lines[1]?.trim() || "";
      setUserInput(activityInput);
    }
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      setUserInput(lines.slice(1).join("\n"));
    }
  };

  useEffect(() => {
    setAnimatedCode("");
    setIsAnimationCompleted(false);

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code]);

  const runCode = () => {
    try {
      let outputMessage = "";
      const trimmedInput = userInput.trim();

      if (currentStep === 1) {
        setName(trimmedInput);
        setCode("Enter a place:\n");
        setUserInput("");
        setCurrentStep(2);
        return;
      } else if (currentStep === 2) {
        setPlace(trimmedInput);
        setCode("Enter an activity:\n");
        setUserInput("");
        setCurrentStep(3);
        return;
      } else if (currentStep === 3) {
        // Ensure activity is properly set before running the story creation
        setActivity(trimmedInput);

        // Directly use trimmedInput for story creation without relying on state
        const createStory = (name, place, activity) => {
          return `One day, ${name} went to ${place} and decided to ${activity}. It was an amazing adventure!`;
        };

        // Use trimmedInput directly for activity value here
        const story = createStory(name, place, trimmedInput);
        outputMessage = story;

        setShowConfetti(true);
      }

      setIsRetry(true);
      setOutput(outputMessage);
      setShowOutput(true);

      setTimeout(() => setShowConfetti(false), 1000);

      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
    } catch (error) {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
    }
  };

  const handleRetry = () => {
    setCode("Enter a name:\n");
    setUserInput("");
    setOutput("");
    setCurrentStep(1);
    setName("");
    setPlace("");
    setActivity("");
    setIsRetry(false);
    setShowOutput(false);
    setShowConfetti(false);
    setAnimatedCode("");
    setIsAnimationCompleted(false);
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #283618, #bc6c25)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #283618, #bc6c25)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700 mb-6">
          Let’s create a fun story generator that uses your inputs to create a
          story!
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <p className="mt-6 text-lg font-bold text-black">INSTRUCTIONS</p>
        <ol className="list-decimal list-inside text-md pl-6 mt-2 text-gray-700">
          <li>
            <strong>
              Set up{" "}
              <a href="https://replit.com/" className="text-blue-500 underline">
                Replit
              </a>
            </strong>
            : Create a new file called{" "}
            <code className="font-mono text-blue-500">story_generator.py</code>.
          </li>
          <li>
            <strong>Write the Code</strong>:
          </li>
        </ol>
        <pre style={codeBlockStyle}>
          {`def create_story(name, place, activity):
    story = f"One day, {name} went to {place} and decided to {activity}. It was an amazing adventure!"
    return story

name = input("Enter a name: ")
place = input("Enter a place: ")
activity = input("Enter an activity: ")

story = create_story(name, place, activity)
print(story)`}
        </pre>
        <p className="text-sm text-gray-700 mt-2">
          Run the code: Create different stories with your own inputs and see
          what happens!
        </p>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#8a946a",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#a4ac86",
              color: "#414833",
              minHeight: "150px",
              borderRadius: "10px",
              overflow: "auto",
              whiteSpace: "pre-wrap",
            }}
          />
        </div>
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "83%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>
          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#ecf39e",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#4f772d",
                }}
              >
                Output:
              </h2>
              <pre
                style={{
                  fontSize: "1rem",
                  color: "#4f772d",
                  marginTop: "10px",
                }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={
                isRetry
                  ? handleRetry
                  : currentStep === 1
                  ? goToNextStep
                  : runCode
              }
              disabled={!userInput.trim()}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: isRetry ? "#4F772D" : "#ECF39E",
                color: isRetry ? "#ECF39E" : "#4F772D",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              {isRetry
                ? "Retry"
                : currentStep === 1
                ? "Next"
                : currentStep === 2
                ? "Next"
                : "Run Code 🎉"}
            </button>
          </div>{" "}
        </div>
      </div>
    </div>
  );
};

export default GodOfThunder;
