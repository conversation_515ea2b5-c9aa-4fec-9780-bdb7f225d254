import React from 'react';

const UnderProcessPage = () => {
  return (
    <div style={styles.container}>
      <h1 style={styles.heading}>This page is under process</h1>
      <p style={styles.message}>Please check back later for updates.</p>
    </div>
  );
};

const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    height: '100vh',
    backgroundColor: '#f5f5f5',
  },
  heading: {
    fontSize: '2rem',
    color: '#333',
  },
  message: {
    fontSize: '1.2rem',
    color: '#666',
  },
};

export default UnderProcessPage;
