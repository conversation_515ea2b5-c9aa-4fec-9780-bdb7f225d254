import Image from "next/image"; // Import Next.js Image component
import React, { useState } from "react";

const HowComputersWork = () => {
  const [matches, setMatches] = useState({
    Input: "",
    Storage: "",
    Processing: "",
    Output: "",
  });
  const [feedback, setFeedback] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const options = ["Processor", "Keyboard", "Monitor", "Hard Drive", "Meow🐱"];

  const correctAnswers = {
    Input: "Keyboard",
    Storage: "Hard Drive",
    Processing: "Processor",
    Output: "Monitor",
  };

  const handleMatch = (part, value) => {
    setMatches((prevMatches) => ({
      ...prevMatches,
      [part]: value,
    }));
  };

  const checkAnswers = () => {
    const newFeedback = {};
    let allCorrect = true;

    for (const [part, correct] of Object.entries(correctAnswers)) {
      if (matches[part] === correct) {
        newFeedback[part] = "🌟 Correct!";
      } else {
        newFeedback[part] = "🚫 Try again!";
        allCorrect = false;
      }
    }

    setFeedback(newFeedback);
    setIsSubmitted(true);

    if (allCorrect) {
      alert("🎉 Great job! You matched all the parts correctly!");
    }
  };

  const resetActivity = () => {
    setMatches({
      Input: "",
      Storage: "",
      Processing: "",
      Output: "",
    });
    setFeedback({});
    setIsSubmitted(false);
  };

  return (
    <div className="text-gray-700 bg-gradient-to-b from-blue-200 via-purple-50 to-pink-100 p-8 min-h-screen rounded-lg">
      <h1 className="text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce">
        How Computers Work
      </h1>
      {/* Fun Illustration at the Top */}
      <div className="mb-8 flex justify-center">
        <Image
          src="/lvl1_img/a-clean-and-modern-educational-illustrat_5raWjKvJSMK6ZvBRud3Q7w_FWwn2-2QTKurLKSC523P9g.jpeg"
          alt="Fun computer illustration"
          width={400}
          height={250}
          className="rounded-lg shadow-lg"
        />
      </div>
      <p className="text-xl mb-6 text-center">
        A computer combines <strong>input</strong>, <strong>storage</strong>,{" "}
        <strong>processing</strong>, and <strong>output</strong> to function.
      </p>
      <ul className="list-disc pl-8 mb-8 text-lg text-purple-900">
        <li>
          <strong>Input:</strong> Devices like a keyboard, mouse, or microphone
          give the computer information.
        </li>
        <li>
          <strong>Memory/Storage:</strong> Computers store your files on a hard
          drive or memory card.
        </li>
        <li>
          <strong>Processing:</strong> The processor, a microchip, processes the
          data with help from a cooling fan.
        </li>
        <li>
          <strong>Output:</strong> Results are shown using devices like screens,
          speakers, or printers.
        </li>
      </ul>
      <div className="bg-blue-50 p-6 rounded-xl shadow-lg border-l-4 border-blue-400">
        <p className="text-lg text-center font-semibold text-blue-800 mb-4">
          <strong>Activity:</strong> Match the computer parts with their
          functions by selecting the correct option below!
        </p>
        {/* Matching Section */}
        <div className="grid grid-cols-2 gap-6">
          {["Input", "Storage", "Processing", "Output"].map((part, index) => (
            <div key={part} className="bg-white p-4 rounded-xl shadow-md">
              <h3 className="text-xl font-semibold mb-4 text-purple-600">
                {index + 1}. {part}
              </h3>
              <select
                className="p-3 border border-gray-300 rounded-xl bg-white shadow-inner w-full"
                value={matches[part]}
                onChange={(e) => handleMatch(part, e.target.value)}
                disabled={isSubmitted}
              >
                <option value="">Select an option</option>
                {options.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
              {isSubmitted && (
                <p
                  className={`mt-4 text-center font-bold ${
                    feedback[part] === "🌟 Correct!"
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {feedback[part]}
                </p>
              )}
            </div>
          ))}
        </div>
        {/* Buttons */}
        <div className="mt-6 flex justify-center gap-6">
          {!isSubmitted ? (
            <button
              className="px-6 py-3 bg-purple-600 text-white font-bold rounded-xl shadow-md hover:bg-purple-700 transition-all"
              onClick={checkAnswers}
            >
              Check Answers
            </button>
          ) : (
            <button
              className="px-6 py-3 bg-yellow-500 text-white font-bold rounded-xl shadow-md hover:bg-yellow-600 transition-all"
              onClick={resetActivity}
            >
              Retry
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default HowComputersWork;
