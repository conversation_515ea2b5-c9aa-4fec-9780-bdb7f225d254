import React, { useState } from "react";
import Image from "next/image";

const WhatIsAProgram = () => {
  return (
    <div className="text-gray-700 bg-gradient-to-b from-blue-100 via-purple-50 to-green-100 p-6 rounded-lg shadow-md">
      <h1 className="text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce">
        What is a Computer Program?
      </h1>
      <p className="text-xl mb-4 text-center">
        A <strong>computer program</strong> is like a recipe! It&apos;s a set of
        instructions that tells the computer how to do cool things like playing
        a game, drawing pictures, or solving problems.
      </p>
      <div className="mt-4 flex justify-center items-center">
        <Image
          src="/lvl1_img/Untitled (3).png"
          alt="A fun computer illustration"
          width={400}
          height={250}
          className="rounded-lg shadow-lg"
        />
      </div>
      <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 mt-6 rounded">
        <p>
          <strong>Fun Fact:</strong> Long ago, people had to write programs to
          perform even simple tasks! Now, we have apps like{" "}
          <em>Microsoft Word</em> and games that make things easier and more
          fun!
        </p>
      </div>
      <p className="text-xl mt-6 text-center">
        <strong>Programs vs. Calculators:</strong> Programs are like smart
        helpers. Calculators only do math, but programs can play music, draw,
        and even play chess!
      </p>
      <div className="bg-green-100 p-4 border-l-4 border-green-500 mt-6 rounded">
        <p>
          <strong>Activity:</strong> Think about the programs or apps you use
          daily. What do they do for you?
        </p>
      </div>
    </div>
  );
};

const DragDropActivity = () => {
  const items = [
    { id: 1, name: "Microsoft Word", correctCategory: "Programs" },
    { id: 2, name: "Calculator", correctCategory: "Programs" },
    { id: 3, name: "Notebook (Paper)", correctCategory: "Not Programs" },
    { id: 4, name: "Chess App", correctCategory: "Programs" },
    { id: 5, name: "Pen", correctCategory: "Not Programs" },
  ];

  const [droppedItems, setDroppedItems] = useState({
    Programs: [],
    "Not Programs": [],
  });
  const [feedback, setFeedback] = useState("");

  const handleDrop = (category, item) => {
    setDroppedItems((prev) => {
      if (prev[category].some((i) => i.id === item.id)) return prev;

      return {
        ...prev,
        [category]: [...prev[category], item],
      };
    });
  };

  const checkAnswers = () => {
    let allCorrect = true;
    for (const [category, itemsInCategory] of Object.entries(droppedItems)) {
      for (const item of itemsInCategory) {
        if (item.correctCategory !== category) {
          allCorrect = false;
        }
      }
    }
    setFeedback(
      allCorrect
        ? "🎉 Great job! All answers are correct!"
        : "❌ Some answers are incorrect. Try again!"
    );
  };

  const resetActivity = () => {
    setDroppedItems({
      Programs: [],
      "Not Programs": [],
    });
    setFeedback("");
  };

  return (
    <div className="p-6 bg-gradient-to-b from-yellow-100 via-white to-blue-100 rounded-lg shadow-md mt-8">
      <h2 className="text-4xl font-bold text-purple-700 text-center mb-6">
        Fun Activity: Programs or Not?
      </h2>
      <p className="text-lg text-center mb-6 text-black">
        Drag and drop the items into the correct category:{" "}
        <strong>Programs</strong> or <strong>Not Programs</strong>.
      </p>

      {/* Draggable Items */}
      <div className="flex gap-4 flex-wrap justify-center text-black">
        {items.map((item) => (
          <div
            key={item.id}
            draggable
            onDragStart={(e) =>
              e.dataTransfer.setData("item", JSON.stringify(item))
            }
            className="p-3 bg-white border border-gray-300 rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-all"
          >
            {item.name}
          </div>
        ))}
      </div>

      {/* Drop Zones */}
      <div className="mt-8 grid grid-cols-2 gap-6">
        {["Programs", "Not Programs"].map((category) => (
          <div
            key={category}
            onDragOver={(e) => e.preventDefault()}
            onDrop={(e) => {
              const data = e.dataTransfer.getData("item");
              const item = JSON.parse(data);
              handleDrop(category, item);
            }}
            className="p-6 bg-blue-50 border border-blue-300 rounded-lg min-h-[150px] shadow-inner flex flex-col items-center"
          >
            <h3 className="text-2xl font-bold text-purple-600 mb-4">
              {category}
            </h3>
            <ul>
              {droppedItems[category].map((item) => (
                <li key={item.id} className="text-lg text-gray-700">
                  {item.name}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* Feedback and Buttons */}
      <div className="mt-8 flex justify-center gap-6">
        <button
          className="px-6 py-3 bg-purple-600 text-white font-bold rounded-lg shadow-md hover:bg-purple-700 transition-all"
          onClick={checkAnswers}
        >
          Check Answers
        </button>
        <button
          className="px-6 py-3 bg-gray-600 text-white font-bold rounded-lg shadow-md hover:bg-gray-700 transition-all"
          onClick={resetActivity}
        >
          Reset
        </button>
      </div>
      {feedback && (
        <p className="mt-6 text-lg font-semibold text-center text-black">{feedback}</p>
      )}
    </div>
  );
};

const App = () => {
  return (
    <div className="p-8 bg-gradient-to-b from-blue-100 via-white to-green-100">
      <WhatIsAProgram />
      <DragDropActivity />
    </div>
  );
};

export default App;
