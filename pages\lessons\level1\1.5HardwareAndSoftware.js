import React, { useState } from "react";
import Image from "next/image";
const HardwareAndSoftware = () => {
  const items = [
    { id: 1, name: "<PERSON>", type: "Hardware", image: "/lvl1_img/download.jpeg" },
    { id: 2, name: "Keyboard", type: "Hardware", image: "/lvl1_img/a-children-s-storybook-illustration-of-a_TWRewv6OSx2ZXmbs_7GdPQ_ZjKDda0IR8mrk4f58u1Wfw.jpeg" },
    { id: 3, name: "Web Browser", type: "Software", image: "/lvl1_img/download (2).jpeg" },
    { id: 4, name: "Video Game", type: "Software", image: "/tetris.png" },
  ];

  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [score, setScore] = useState(0);
  const [feedback, setFeedback] = useState(null);
  const [completed, setCompleted] = useState(false);

  const handleAnswer = (answer) => {
    const currentItem = items[currentItemIndex];
    const isCorrect = answer === currentItem.type;

    if (isCorrect) {
      setScore((prevScore) => prevScore + 1);
      setFeedback("🎉 Correct! Great job!");
    } else {
      setFeedback("❌ Oops, that's not correct. Try again!");
    }

    setTimeout(() => {
      if (currentItemIndex + 1 < items.length) {
        setCurrentItemIndex((prevIndex) => prevIndex + 1);
        setFeedback(null);
      } else {
        setCompleted(true);
      }
    }, 1000);
  };

  const resetQuiz = () => {
    setCurrentItemIndex(0);
    setScore(0);
    setFeedback(null);
    setCompleted(false);
  };

  return (
    <div className="text-gray-700 p-6 bg-gradient-to-b from-blue-50 via-white to-green-50">
      {/* Title and Description */}
      <h1 className="text-4xl font-bold mb-6 text-purple-700 text-center">
        Hardware vs. Software
      </h1>
         <div className="flex justify-center mb-6">
              <Image
                src="/lvl1_img/a-children-s-storybook-illustration-of-a_J3vVFxGhRDS1Kmx2vm5HWw_0yQljxkMRMGI2wW4RGanxA.jpeg" // Replace with your image path
                alt="Operating System Illustration"
                width={500}
                height={300}
                className="rounded-lg shadow-md"
              />
            </div>
      <p className="text-lg mb-4 text-center">
        Computers are made up of <strong>hardware</strong> and <strong>software</strong>. Let's learn the difference!
      </p>
      <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 mb-6 rounded-lg shadow-md">
        <p>
          <strong>Hardware:</strong> These are the physical parts of a computer that you can see and touch, like a mouse or keyboard.
        </p>
        <p className="mt-2">
          <strong>Software:</strong> These are the programs that make the computer do cool stuff, like games or web browsers!
        </p>
      </div>

      {/* Activity Section */}
      {!completed ? (
        <div className="flex flex-col items-center">
          {/* Image of the current item */}
          <div className="mb-4">
            <img
              src={items[currentItemIndex].image}
              alt={items[currentItemIndex].name}
              className="w-48 h-48 border border-gray-300 rounded-lg shadow-md"
            />
          </div>
          <p className="text-xl font-bold mb-4">{items[currentItemIndex].name}</p>
          <div className="space-x-4">
            <button
              onClick={() => handleAnswer("Hardware")}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Hardware
            </button>
            <button
              onClick={() => handleAnswer("Software")}
              className="px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600"
            >
              Software
            </button>
          </div>
          {feedback && (
            <p
              className={`mt-4 text-lg font-bold ${
                feedback.includes("Correct") ? "text-green-600" : "text-red-600"
              }`}
            >
              {feedback}
            </p>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <p className="text-2xl font-bold mb-4">Quiz Completed!</p>
          <p className="text-lg mb-4">
            Your Score: <strong>{score}/{items.length}</strong>
          </p>
          <button
            onClick={resetQuiz}
            className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600"
          >
            Retry Quiz
          </button>
        </div>
      )}
    </div>
  );
};

export default HardwareAndSoftware;
