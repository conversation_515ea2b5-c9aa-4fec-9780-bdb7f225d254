import React from "react";
import Link from "next/link";

const LandingPage = () => {
  const steps = [
    { id: 1, title: "What is Scratch?", link: "/lessons/level1/step-1" },
    { id: 2, title: "Set Up Scratch", link: "/lessons/level1/step-2" },
    { id: 3, title: "Create Your First Project", link: "/lessons/level1/step-3" },
  ];

  return (
    <div className="text-gray-700">
      <header className="bg-purple-600 text-white py-6">
        <h1 className="text-4xl font-bold text-center">Getting Started with Scratch</h1>
        <p className="text-center mt-2">
          Learn Scratch step by step and start creating amazing projects today!
        </p>
      </header>

      <main className="max-w-4xl mx-auto mt-8">
        <h2 className="text-2xl font-semibold mb-4">Learn Step by Step</h2>
        <ul className="space-y-4">
          {steps.map((step) => (
            <li key={step.id} className="p-4 bg-white rounded-lg shadow-md">
              <h3 className="text-lg font-semibold">{step.title}</h3>
              <Link href={step.link}>
                <button className="mt-2 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                  Go to Step {step.id}
                </button>
              </Link>
            </li>
          ))}
        </ul>
      </main>

      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default LandingPage;
