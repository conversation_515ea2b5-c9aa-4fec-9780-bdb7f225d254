import React, { useRef, useState, useEffect } from "react";

const IoTMatchingActivity = () => {
  const canvasRef = useRef(null);
  const [connections, setConnections] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);
  const [feedback, setFeedback] = useState("");

  const devices = [
    { id: "Smart Fridge", x: 100, y: 80, label: "Smart Fridge", icon: "🧊" },
    { id: "Smart Bulb", x: 100, y: 220, label: "Smart Bulb", icon: "💡" },
    { id: "Smart Watch", x: 100, y: 360, label: "Smart Watch", icon: "⌚" },
  ];

  const correctMatches = {
    "Smart Fridge": "Internet",
    "Smart Bulb": "Internet",
    "Smart Watch": "Internet",
  };

  const internet = { id: "Internet", x: 550, y: 220, label: "Internet", icon: "☁️" };

  useEffect(() => {
    drawAllConnections();
  }, [connections]);

  const startDrag = (device) => {
    setDraggedItem(device);
    setFeedback(""); // Clear feedback while dragging
  };

  const handleDrop = (target) => {
    if (draggedItem) {
      const isAlreadyConnected = connections.some(
        (connection) => connection.from.id === draggedItem.id
      );

      if (isAlreadyConnected) {
        setFeedback(`⚠️ ${draggedItem.label} is already connected!`);
        setDraggedItem(null);
        return;
      }

      const isCorrect = correctMatches[draggedItem.id] === target.id;

      setConnections((prev) => [
        ...prev,
        { from: draggedItem, to: target, isCorrect },
      ]);

      setFeedback(
        isCorrect
          ? `✅ Signal Sent! ${draggedItem.label} connected to the Internet Cloud!`
          : `❌ Oops! ${draggedItem.label} couldn't connect. Try again!`
      );

      setDraggedItem(null); // Reset dragged item
    }
  };

  const drawAllConnections = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear previous lines

      connections.forEach((connection) => {
        drawLine(connection.from, connection.to, connection.isCorrect, ctx);
      });
    }
  };

  const drawLine = (from, to, isCorrect, ctx) => {
    ctx.beginPath();
    ctx.moveTo(from.x + 50, from.y + 40); // Start position (IoT device)
    ctx.lineTo(to.x + 60, to.y + 40); // End position (Internet)
    ctx.strokeStyle = isCorrect ? "#4CAF50" : "#F44336"; // Green for correct, red for incorrect
    ctx.lineWidth = 2;
    ctx.stroke();
  };

  return (
    <div className="flex flex-col items-center text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-50 p-6">
      <h1 className="text-4xl font-bold mb-6 bg-white text-purple-700">IoT Signal Matching</h1>

      {/* IoT Content Section */}
      <div className="mb-6 w-full max-w-4xl text-left">
        <h2 className="text-2xl font-bold mb-4">What is the Internet of Things (IoT)?</h2>
        <p className="text-lg mb-4">
          <strong>The Internet of Things, or IoT, is like a magic network that connects everyday objects to the internet!</strong> Imagine if
          your toys, toothbrush, or even your fridge could talk to each other and to you. With IoT, this is possible because tiny computers
          called sensors are put into everyday items. These sensors can collect information, like how often you brush your teeth, and send
          that data to other devices or even your smartphone.
        </p>
        <img
          src="/lvl1_img/Untitled (7).png"
          alt="IoT illustration"
          className="w-full mb-4 rounded-lg shadow-md"
        />
        <p className="text-lg">
          <strong>Fun Fact:</strong> Thanks to IoT, your home could become a "smart home" where lights turn off automatically when you leave
          the room, or your fridge could remind you when you're out of milk! How cool is that?
        </p>
      </div>

      {/* How to Play Section */}
      <div className="mb-6 w-full max-w-4xl text-center bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">How to Play</h2>
        <p className="text-lg">
          Drag and drop each IoT device (e.g., Smart Fridge, Smart Bulb) to the Internet Cloud to connect them. Watch the lines appear
          between the devices and the cloud! Each correct connection will be marked as "Correct" in the history below. Can you connect all
          the devices?
        </p>
      </div>

      {/* Interactive Area */}
      <div className="relative p-8 rounded-lg w-full max-w-4xl h-96">
        {/* Canvas for Drawing Lines */}
        <canvas
          ref={canvasRef}
          width={700}
          height={400}
          className="absolute top-0 left-0 pointer-events-none"
        />

        {/* IoT Devices */}
        {devices.map((device) => (
          <div
            key={device.id}
            className="w-24 h-24 bg-white rounded-full shadow-md flex flex-col items-center justify-center cursor-pointer border-4 border-yellow-400"
            style={{
              position: "absolute",
              top: device.y,
              left: device.x,
            }}
            draggable
            onDragStart={() => startDrag(device)}
          >
            <span className="text-3xl">{device.icon}</span>
            <p className="mt-1 text-xs font-bold text-gray-800">{device.label}</p>
          </div>
        ))}

        {/* Internet Cloud */}
        <div
          className="w-48 h-48 bg-white rounded-full shadow-md border-4 border-blue-400 flex flex-col items-center justify-center cursor-pointer"
          style={{
            position: "absolute",
            top: internet.y,
            left: internet.x,
          }}
          onDrop={() => handleDrop(internet)}
          onDragOver={(e) => e.preventDefault()}
        >
          <span className="text-5xl">{internet.icon}</span>
          <p className="mt-2 text-sm font-bold text-gray-800">{internet.label}</p>
        </div>
      </div>

      {/* Feedback */}
      {feedback && (
        <div
          className={`mt-4 px-4 py-2 rounded-lg shadow-md w-full max-w-2xl text-center ${
            feedback.startsWith("✅")
              ? "bg-green-100 text-green-700"
              : feedback.startsWith("⚠️")
              ? "bg-yellow-100 text-yellow-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          {feedback}
        </div>
      )}

      {/* Connection History */}
      <div className="mt-8 w-full max-w-4xl text-center">
        <h2 className="text-2xl font-bold mb-4">Connections</h2>
        {connections.length > 0 ? (
          <ul className="space-y-2">
            {connections.map((connection, index) => (
              <li
                key={index}
                className={`font-bold ${
                  connection.isCorrect ? "text-green-700" : "text-red-700"
                }`}
              >
                {connection.from.label} → {connection.to.label} (
                {connection.isCorrect ? "Correct" : "Wrong"}).
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-lg text-gray-500">No connections made yet.</p>
        )}
      </div>
    </div>
  );
};

export default IoTMatchingActivity;
