import React, { useState } from "react";

const HowComputerVisionWorks = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const steps = [
    {
      id: 1,
      title: "Seeing the World",
      description:
        "Cameras and sensors capture images or videos, just like taking a picture with your phone!",
      action: "Click to see the captured image!",
      image: "/lvl1_img/Smart453ForFour_RVC_Installed.png",
    },
    {
      id: 2,
      title: "Understanding the Image",
      description:
        "The computer breaks the image into tiny pieces (pixels) and analyzes them. These pixels are just tiny colored dots!",
      action: "Click to see the pixelated version of the image!",
      image: "/lvl1_img/download (1).png",
    },
    {
      id: 3,
      title: "Learning to Recognize",
      description:
        "The computer compares this image with thousands of examples to understand what it is looking at. It's like studying for a test!",
      action: "Click to see the computer's guess!",
      image: "/lvl1_img/pxArt (1).png",
    },
    {
      id: 4,
      title: "Making Decisions",
      description:
        "Finally, the computer identifies the object and decides what action to take, like recognizing a stop sign and stopping the car.",
      action: "Click to see the computer's decision!",
      image: "/lvl1_img/stop-stamp-7.png",
    },
  ];

  const handleNextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handleReset = () => {
    setCurrentStep(0);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-100 p-8 text-gray-700">
      <h1 className="text-4xl font-extrabold mb-6 text-center text-purple-700">
        How Does Computer Vision Work?
      </h1>
      <p className="text-lg text-center mb-8">
        Computer Vision works in several steps to "see" and understand the world. Let’s explore each step!
      </p>

      {/* Current Step Content */}
      <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-8">
        <h2 className="text-2xl font-bold text-blue-600 mb-4 text-center">
          {steps[currentStep].title}
        </h2>
        <p className="text-lg text-gray-600 text-center mb-6">
          {steps[currentStep].description}
        </p>
        <div className="flex flex-col items-center">
          <img
            src={steps[currentStep].image}
            alt={steps[currentStep].title}
            className="w-48 h-48 mb-6 border-2 border-gray-200 rounded-lg shadow-md"
          />
          <button
            onClick={handleNextStep}
            className={`px-6 py-3 rounded-lg text-lg font-semibold ${
              currentStep === steps.length - 1
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-blue-500 text-white hover:bg-blue-600"
            }`}
            disabled={currentStep === steps.length - 1}
          >
            {steps[currentStep].action}
          </button>
        </div>
      </div>

      {/* Reset Button */}
      {currentStep === steps.length - 1 && (
        <div className="mt-6 text-center">
          <button
            onClick={handleReset}
            className="px-6 py-3 bg-red-500 text-white rounded-lg font-bold hover:bg-red-600"
          >
            Start Over
          </button>
        </div>
      )}

      {/* Real-Life Example Section */}
      <div className="mt-12 bg-green-100 p-6 rounded-lg border-l-4 border-green-500 shadow-md max-w-4xl mx-auto">
        <h2 className="text-xl font-bold text-green-800 mb-2">
          Real-Life Example
        </h2>
        <p className="text-lg text-gray-700">
          In a self-driving car, computer vision can recognize a stop sign and
          tell the car to stop. It goes through all these steps in just seconds!
        </p>
      </div>
    </div>
  );
};

export default HowComputerVisionWorks;
