import React, { useState } from "react";

const GuessTheVision = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [feedback, setFeedback] = useState("");

  const questions = [
    {
      id: 1,
      image: "/lvl1_img/download (1).png",
      question: "What should the self-driving car do when it sees this?",
      options: ["Stop", "Speed Up", "Turn"],
      correct: "Stop",
    },
    {
      id: 2,
      image: "/lvl1_img/caerra.jpeg",
      question: "What is the traffic camera likely analyzing?",
      options: ["Weather", "Traffic Flow", "Birds"],
      correct: "Traffic Flow",
    },
    {
      id: 3,
      image: "/lvl1_img/download (5).jpeg",
      question: "What can computer vision help doctors do with this X-ray?",
      options: ["Diagnose Diseases", "Take Photos", "Print Reports"],
      correct: "Diagnose Diseases",
    },
    {
      id: 4,
      image: "/lvl1_img/download (6).jpeg",
      question: "What can computer vision do in a factory?",
      options: ["Check Product Quality", "Print Instructions", "Turn Off Machines"],
      correct: "Check Product Quality",
    },
    {
      id: 5,
      image: "/lvl1_img/download (7).jpeg",
      question: "What is computer vision likely doing in this store?",
      options: ["Tracking Shopper Behavior", "Counting Birds", "Watching TV"],
      correct: "Tracking Shopper Behavior",
    },
  ];

  const handleAnswer = (option) => {
    if (option === questions[currentQuestion].correct) {
      setFeedback("✅ Correct! Great job!");
    } else {
      setFeedback("❌ Incorrect. Try again!");
    }
  };

  const nextQuestion = () => {
    setFeedback("");
    setCurrentQuestion((prev) => Math.min(prev + 1, questions.length - 1));
  };

  const resetActivity = () => {
    setFeedback("");
    setCurrentQuestion(0);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-100 p-8 text-gray-700">
      <h1 className="text-4xl font-extrabold text-center mb-6 text-purple-700">
        Guess the Vision
      </h1>
      <p className="text-lg text-center mb-6">
        Can you guess what the computer should do in these scenarios? Let’s find out!
      </p>

      {/* Question Section */}
      <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-6">
        <div className="flex flex-col items-center">
          {questions[currentQuestion].image && (
            <img
              src={questions[currentQuestion].image}
              alt="Question Image"
              className="w-48 h-48 mb-4 border border-gray-300 rounded-md shadow-md"
            />
          )}
          <p className="text-lg font-semibold text-gray-800 mb-4">
            {questions[currentQuestion].question}
          </p>
        </div>

        {/* Options */}
        <div className="grid grid-cols-1 gap-4">
          {questions[currentQuestion].options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleAnswer(option)}
              className="w-full px-6 py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600"
            >
              {option}
            </button>
          ))}
        </div>
      </div>

      {/* Feedback Section */}
      {feedback && (
        <div
          className={`max-w-4xl mx-auto mt-6 p-4 rounded-lg shadow-md ${
            feedback.startsWith("✅")
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          <p className="text-center text-lg font-bold">{feedback}</p>
        </div>
      )}

      {/* Next Question Button */}
      {currentQuestion < questions.length - 1 && feedback.startsWith("✅") && (
        <div className="text-center mt-6">
          <button
            onClick={nextQuestion}
            className="px-6 py-3 bg-green-500 text-white rounded-lg shadow-md hover:bg-green-600"
          >
            Next Question
          </button>
        </div>
      )}

      {/* Completion Section */}
      {currentQuestion === questions.length - 1 && feedback.startsWith("✅") && (
        <div className="mt-6 max-w-4xl mx-auto bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
          <p className="text-lg font-bold text-center">
            🎉 You've completed the activity! Great job!
          </p>
          <div className="text-center mt-4">
            <button
              onClick={resetActivity}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Play Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GuessTheVision;
