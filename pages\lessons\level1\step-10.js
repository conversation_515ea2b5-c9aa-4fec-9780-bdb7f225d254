import React from "react";
import Link from "next/link";

const StepTen = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 10: Loops and Conditions</h1>
        <p className="text-center mt-2 text-lg">Let’s make your sprite smarter with loops and choices!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What Are Loops and Conditions?</h2>
          <p className="text-lg mb-4">
            Loops make your sprite do the same thing again and again. Conditions let your sprite make decisions, like saying "Hello!" if you
            press a button.
          </p>

          {/* Loops */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Loops: Repeat Actions</h3>
          <p className="text-lg mb-4">
            Loops help your sprite do something over and over. Use the "forever" loop to keep going or "repeat" to do it a certain number of
            times!
          </p>
          {/* Image Placeholder */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 151838.png"
              alt="Loops"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Conditions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Conditions: Make Choices</h3>
          <p className="text-lg mb-4">
            Conditions are like questions. Your sprite checks if something is true, like "Did I touch the edge?" and does something if it is
            true!
          </p>
          {/* Image Placeholder */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 151925.png"
              alt="Conditions"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Add Loops and Conditions:</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Add a Loop:</strong> Drag a "forever" or "repeat" block from the Control section into your script.
            </li>
            <li>
              <strong>Add a Condition:</strong> Drag an "if" block from the Control section. Add something like "touching edge?" from the
              Sensing section.
            </li>
            <li>
              <strong>Test It:</strong> Click the green flag to see your sprite follow the loop or make a choice!
            </li>
          </ul>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Add a Loop and a Choice!</h3>
            <p className="text-lg mb-4">
              Use a loop to make your sprite move or spin. Then, add a condition to make it say "Hello!" when it touches the edge.
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-9">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 9</button>
        </Link>
        <Link href="/lessons/level1/step-11">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 11</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepTen;
