import React from "react";
import Link from "next/link";

const StepEleven = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 11: Variables and Data</h1>
        <p className="text-center mt-2 text-lg">Let’s learn how to remember numbers and scores using variables!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What Are Variables?</h2>
          <p className="text-lg mb-4">
            A variable is like a box that holds information. You can use variables to remember things like scores, timers, or how many steps
            your sprite has taken.
          </p>

          {/* Examples of Variables */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">What Can Variables Do?</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Track Scores:</strong> Use a variable to keep track of points in a game.
            </li>
            <li>
              <strong>Count Steps:</strong> Make your sprite count how far it has moved.
            </li>
            <li>
              <strong>Set Timers:</strong> Use a variable to create a countdown or stopwatch.
            </li>
          </ul>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 152200.png"
              alt="Variables"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Use Variables:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Create a Variable:</strong>
              <p className="text-lg mt-2">
                Go to the "Variables" section in the blocks area. Click "Make a Variable" and give it a name like "Score" or "Steps."
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 152244.png"
                  alt="Placeholder: Create Variable"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Change a Variable:</strong>
              <p className="text-lg mt-2">
                Drag the "change [variable] by [1]" block into your script. This will increase or decrease the variable’s value.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 152316.png"
                  alt="Placeholder: Change Variable"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Show the Variable:</strong>
              <p className="text-lg mt-2">
                The variable will appear on the stage. Watch it update as your script runs!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 152330.png"
                  alt="Placeholder: Show Variable"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Create a Scoring System!</h3>
            <p className="text-lg mb-4">
              Make a variable called "Score." Add 1 point every time your sprite touches another sprite. Can you make a game out of it?
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-10">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 10</button>
        </Link>
        <Link href="/lessons/level1/step-12">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 12</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepEleven;
