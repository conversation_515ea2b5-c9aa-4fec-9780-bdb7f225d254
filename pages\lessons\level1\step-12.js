import React from "react";
import Link from "next/link";

const StepTwelve = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 12: Share Your Project</h1>
        <p className="text-center mt-2 text-lg">Let’s share your amazing creation with the world!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">Why Share Your Project?</h2>
          <p className="text-lg mb-4">
            Sharing your project lets your friends and family see your work. You can also get ideas and feedback from the Scratch
            community!
          </p>

          {/* Steps to Share */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Share Your Project:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Log In:</strong>
              <p className="text-lg mt-2">
                Make sure you are logged into your Scratch account. If you don’t have one, ask an adult to help you create one.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 153010.png"
                  alt="Placeholder: Log In"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Click "Share":</strong>
              <p className="text-lg mt-2">
                Open your project in the Scratch editor. Click the orange "Share" button at the top of the screen.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 152707.png"
                  alt="Placeholder: Click Share"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Add Notes and Credits:</strong>
              <p className="text-lg mt-2">
                Write a short description of your project in the "Notes and Credits" section. Tell others what your project is about or how
                you made it!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 152816.png"
                  alt="Placeholder: Notes and Credits"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 4 */}
            <li>
              <strong>Share the Link:</strong>
              <p className="text-lg mt-2">
                Once your project is shared, copy the link from the address bar and send it to your friends, family, or teacher!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 153045.png"
                  alt="Placeholder: Share Link"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 153113.png"
              alt="Share Your Project"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Share and Explore!</h3>
            <p className="text-lg mb-4">
              Share your project with someone you know. Then, explore the Scratch community to see other amazing projects. Can you find
              inspiration for your next creation?
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-11">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 11</button>
        </Link>
        <Link href="/lessons/level1/step-13">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 13</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepTwelve;
