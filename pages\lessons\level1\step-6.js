import React from "react";
import Link from "next/link";

const StepSix = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 6: Learn About Sprites</h1>
        <p className="text-center mt-2 text-lg">
          Sprites are the stars of your Scratch projects. Let’s learn all about them!
        </p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What Are Sprites?</h2>
          <p className="text-lg mb-4">
            Sprites are the characters or objects in your Scratch project. They can move, talk, change their look, and do whatever you want
            them to do!
          </p>

          {/* Features of Sprites */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Cool Things Sprites Can Do:</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Move Around:</strong> Use motion blocks to make your sprite walk, jump, or fly!
            </li>
            <li>
              <strong>Talk:</strong> Add speech or thought bubbles to make your sprite talk or share its thoughts.
            </li>
            <li>
              <strong>Change Costumes:</strong> Give your sprite different looks by switching its costume.
            </li>
            <li>
              <strong>Interact:</strong> Sprites can interact with each other or with the stage to make your project more exciting.
            </li>
          </ul>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 142803.png"
              alt="Sprites in Action"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Add a Sprite:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Choose a Sprite:</strong>
              <p className="text-lg mt-2">
                Click on the "Choose a Sprite" button in the bottom-right corner of the Scratch editor. You can select from hundreds of
                pre-made sprites!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 143647.png"
                  alt="Placeholder: Choose a Sprite"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Draw Your Own Sprite:</strong>
              <p className="text-lg mt-2">
                Want to get creative? Click on the "Paint" option to draw your own sprite. Use the drawing tools to create anything you can
                imagine!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 143715.png"
                  alt="Placeholder: Paint a Sprite"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Upload a Sprite:</strong>
              <p className="text-lg mt-2">
                Have your own image or drawing? Click on the "Upload" option to turn it into a sprite for your project.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 150227.png"
                  alt="Placeholder: Upload a Sprite"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Add Your First Sprite!</h3>
            <p className="text-lg mb-4">
              Try adding a sprite to your Scratch project. Choose one from the library, draw your own, or upload an image. What will you
              create?
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-5">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 5</button>
        </Link>
        <Link href="/lessons/level1/step-7">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 7</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepSix;
