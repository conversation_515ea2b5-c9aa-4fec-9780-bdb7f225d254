import React from "react";
import Link from "next/link";

const StepNine = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 9: Blocks and Categories</h1>
        <p className="text-center mt-2 text-lg">
          Blocks are the building blocks of Scratch. Let’s learn how to use them!
        </p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What Are Blocks?</h2>
          <p className="text-lg mb-4">
            Blocks are commands that tell your sprites what to do. You can connect them like puzzle pieces to create animations, games, or
            stories.
          </p>

          {/* Categories of Blocks */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Categories of Blocks:</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Motion:</strong> Use these blocks to move your sprite, make it glide, or point in a direction.
            </li>
            <li>
              <strong>Looks:</strong> Change your sprite’s appearance, make it talk, or switch costumes.
            </li>
            <li>
              <strong>Sound:</strong> Add sounds, music, or effects to your project.
            </li>
            <li>
              <strong>Events:</strong> These blocks trigger actions, like "when green flag clicked."
            </li>
            <li>
              <strong>Control:</strong> Use loops or wait commands to control your project’s flow.
            </li>
            <li>
              <strong>Sensing:</strong> Detect events like touching a color or listening for sounds.
            </li>
            <li>
              <strong>Operators:</strong> Perform math calculations or compare values.
            </li>
            <li>
              <strong>Variables:</strong> Create variables to store and change data.
            </li>
          </ul>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 151547.png"
              alt="Scratch Blocks"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Use Blocks:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Find a Block:</strong>
              <p className="text-lg mt-2">
                Select a category on the left-hand side of the Scratch editor. Drag the block you want into the scripting area.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 151614.png"
                  alt="Placeholder: Find Block"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Connect Blocks:</strong>
              <p className="text-lg mt-2">
                Drag and connect blocks together like puzzle pieces. They’ll snap into place when connected.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 151622.png"
                  alt="Placeholder: Connect Blocks"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Test Your Code:</strong>
              <p className="text-lg mt-2">
                Click the green flag to run your code. Adjust the blocks if needed to make your project work just right!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 144406.png"
                  alt="Placeholder: Test Code"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Build Your First Script!</h3>
            <p className="text-lg mb-4">
              Create a script that makes your sprite move, talk, or play a sound. Use at least three different types of blocks in your
              script!
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-8">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 8</button>
        </Link>
        <Link href="/lessons/level1/step-10">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 10</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepNine;
