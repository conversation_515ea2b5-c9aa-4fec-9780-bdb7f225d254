import React, { useState, useEffect } from "react";
import { Player } from "@lottiefiles/react-lottie-player";

const arrowAnimationUrl = "https://assets3.lottiefiles.com/packages/lf20_yd8fbnml.json";
const metaverseAnimationUrl = "https://assets6.lottiefiles.com/private_files/lf30_l5x7wnlm.json";
const virtualWorldAnimationUrl = "https://assets5.lottiefiles.com/packages/lf20_vvwrxbx9.json";
const robotAnimationUrl = "https://assets9.lottiefiles.com/packages/lf20_rlzitsb7.json";
const gadgetAnimationUrl = "https://assets2.lottiefiles.com/packages/lf20_9Oqzm7.json";

// New Lottie Emoji URLs
const newEmojiUrls = [
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f6f8/lottie.json", // UFO
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f98b/lottie.json", // Bug
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f996/lottie.json", // T-Rex
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f32a_fe0f/lottie.json", // Tornado
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f916/lottie.json", // Robot Face
];

const ArrowIcon = () => (
  <Player autoplay loop src={arrowAnimationUrl} style={{ height: 40, width: 40, marginRight: 10 }} />
);

const DisplayLevel2 = () => {
  const headings = [
    "Virtual Reality Goggles",
    "Holographic Projectors",
    "Teleportation Pads",
    "AI Pet Companions",
    "Flying Hoverboards",
    "Mind-Reading Helmets",
    "Gravity-Defying Shoes",
    "Digital Tree Houses",
  ];

  const bgColors = ["#FF9AA2", "#FFB7B2", "#FFDAC1", "#E2F0CB", "#B5EAD7", "#C7CEEA", "#F1E8B8", "#E2BEF1"];
  const [currentIndex, setCurrentIndex] = useState(0);
  const [bgColor, setBgColor] = useState(bgColors[0]);
  const [emojiElements, setEmojiElements] = useState([]);

  // Generate emoji positions after component mounts
  useEffect(() => {
    const elements = Array(10)
      .fill(null)
      .map(() => ({
        left: Math.random() * 100,
        top: Math.random() * 100,
        delay: Math.random() * 5,
        emojiUrl: newEmojiUrls[Math.floor(Math.random() * newEmojiUrls.length)],
      }));
    setEmojiElements(elements);
  }, []);

  // Cycle through headings and background colors
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % headings.length;
        setBgColor(bgColors[nextIndex]);
        return nextIndex;
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 relative overflow-hidden">
      {/* Title */}
      <h1 className="text-5xl font-bold text-white mb-6">Welcome to the Metaverse!</h1>

      {/* Subtitle */}
      <p className="text-2xl text-white mb-6">Discover Amazing Gadgets in the World of Tomorrow!</p>

      {/* Animated Headings */}
      <div
        className="p-6 rounded-lg text-white font-bold text-xl text-center transition-colors duration-500"
        style={{ backgroundColor: bgColor }}
      >
        <ArrowIcon />
        {headings[currentIndex]}
      </div>

      {/* Lottie Animations */}
      <Player
        autoplay
        loop
        src={metaverseAnimationUrl}
        style={{ position: "absolute", width: "200px", left: "5%", top: "20%" }}
      />
      <Player
        autoplay
        loop
        src={virtualWorldAnimationUrl}
        style={{ position: "absolute", width: "200px", right: "5%", top: "30%" }}
      />
      <Player
        autoplay
        loop
        src={robotAnimationUrl}
        style={{ position: "absolute", width: "150px", bottom: "10%", left: "50%", transform: "translateX(-50%)" }}
      />
      <Player
        autoplay
        loop
        src={gadgetAnimationUrl}
        style={{ position: "absolute", width: "180px", top: "60%", left: "15%" }}
      />

      {/* Floating Emoji Animations */}
      {emojiElements.map((element, index) => (
        <Player
          key={index}
          autoplay
          loop
          src={element.emojiUrl}
          style={{
            position: "absolute",
            width: "100px",
            height: "100px",
            left: `${element.left}%`,
            top: `${element.top}%`,
            animationDelay: `${element.delay}s`,
          }}
        />
      ))}
    </div>
  );
};

export default DisplayLevel2;
