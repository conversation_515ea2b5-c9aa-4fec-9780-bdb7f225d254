'use client';
import React, { useState } from 'react';
import PropTypes from "prop-types"; // Import PropTypes for prop validation


// Import lesson components
import IntroductionToDataScience from './1.0IntroductionToDataScience';
import GlitchSaysHi from './1.1GlitchSaysHi';
import WhatIsDataScience from './1.2WhatIsDataScience';
import HowDataIsCollected from './2.1HowDataIsCollected';
import UnderstandingMeanMedianMode from './2.2UnderstandingMeanMedianMode';
import WhyVisualizeData from './3.1WhyVisualizeData';
import CreateYourOwnBarGraph from './3.2CreateYourOwnBarGraph';
import UnderstandingDataScience from './1.3UnderstandingDataScience';
import ExploringDataTypes from './2.3ExploringDataTypes';
import IntroductionToCharts from './3.3IntroductionToCharts';
import DataStorytelling from './3.4DataStorytelling';
import AdvancedVisualization from './3.5AdvancedVisualization';
import Conclusion from './4.1Conclusion';
import DataVisualization from './3.0DataVisualization';
import QuizApp from './4.0Quiz';

// Modal Component
const Modal = ({ isOpen, onClose, children }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            {/* Background overlay */}
            <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>

            {/* Modal content */}
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 relative z-10 shadow-lg">
                <button onClick={onClose} className="absolute top-2 right-2 text-gray-600 hover:text-gray-800">
                    ✖️
                </button>
                {children}
            </div>
        </div>
    );
};

// Prop Validation for Modal Component
Modal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    children: PropTypes.node.isRequired,
  };

// IndexOfCourse Component
const IndexOfCourse = () => {
    return (
        <div className="p-4">
            <div className="mt-4">
                <h3 className="text-lg font-semibold">CONTENTS</h3>
                <ul className="list-disc list-inside">
                    <li>
                        <strong>01. INTRODUCTION TO DATA SCIENCE</strong>
                        <ul className="ml-4">
                            <li>Introduction to Data Science</li>
                            <li>Glitch Says Hi</li>
                            <li>What is Data Science?</li>
                            <li>Understanding Data Science</li>
                        </ul>
                    </li>
                    <li>
                        <strong>02. DATA COLLECTION AND ANALYSIS</strong>
                        <ul className="ml-4">
                            <li>How Data is Collected</li>
                            <li>Understanding Mean, Median, and Mode</li>
                            <li>Exploring Data Types</li>
                        </ul>
                    </li>
                    <li>
                        <strong>03. DATA VISUALIZATION</strong>
                        <ul className="ml-4">
                            <li>What is Data Visualization?</li>
                            <li>Why Visualize Data?</li>
                            <li>Create Your Own Bar Graph</li>
                            <li>Introduction to Charts</li>
                            <li>Data Storytelling</li>
                            <li>Advanced Visualization</li>
                        </ul>
                    </li>
                    <li>
                        <strong>04. QUIZ</strong>
                        <ul className="ml-4">
                            <li>Data Science Quiz</li>
                        </ul>
                    </li>
                    <li>
                        <strong>05. FINISH LINE</strong>
                        <ul className="ml-4">
                            <li>Conclusion</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    );
};

// Main Component
export default function CourseContentModal() {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const handleOpenModal = () => setIsModalOpen(true);
    const handleCloseModal = () => setIsModalOpen(false);

    return (
        <div className="max-w-3xl mx-auto p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Course Section</h1>
            
            <button
                onClick={handleOpenModal}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
                Index of Course
            </button>

            {/* Modal to show IndexOfCourse component */}
            <Modal isOpen={isModalOpen} onClose={handleCloseModal}>
                <IndexOfCourse />
            </Modal>
        </div>
    );
}

// Export level3Data as a named export
export const level3Data = {
  majorTopics: [
    {
      title: "WELCOME TO DATA SCIENCE",
      minorTopics: [
        { title: 'Introduction to Data Science', component: <IntroductionToDataScience /> },
        { title: "Glitch Says Hi", component: <GlitchSaysHi /> },
        { title: "What is Data Science?", component: <WhatIsDataScience /> },
        { title: "Understanding Data Science", component: <UnderstandingDataScience /> },
      ],
    },
    {
      title: "DATA COLLECTION AND ANALYSIS",
      minorTopics: [
        { title: "How Data is Collected", component: <HowDataIsCollected /> },
        { title: "Understanding Mean, Median, and Mode", component: <UnderstandingMeanMedianMode /> },
        { title: "Exploring Data Types", component: <ExploringDataTypes /> }, // New Component
      ],
    },
    {
      title: "DATA VISUALIZATION",
      minorTopics: [
        { title: "What is Data Visualization?", component: <DataVisualization /> },
        { title: "Why Visualize Data?", component: <WhyVisualizeData /> },
        { title: "Create Your Own Bar Graph", component: <CreateYourOwnBarGraph /> },
        { title: "Introduction to Charts", component: <IntroductionToCharts /> },
        { title: "Data Storytelling", component: <DataStorytelling /> },
        { title: "Advanced Visualization", component: <AdvancedVisualization /> },
      ],
    },
    {
      title: "QUIZ: Test Your Knowledge",
      minorTopics: [
        { title: "Data Science Quiz", component: <QuizApp/> },
      ],
    },
    {
      title: "FINISH LINE",
      minorTopics: [
        { title: "Conclusion", component: <Conclusion /> },
      ],
    },
  ],
};
