import React, { useState, useEffect } from "react";
import { Player } from "@lottiefiles/react-lottie-player";

const dataScienceIntroAnimationUrl =
  "https://assets6.lottiefiles.com/packages/lf20_7p7qv0hl.json"; // Data Science intro animation
const robotMascotPath = "./animations/robotMascot"; // Path to your local JSON file

export default function IntroductionToDataScience() {
  const [showFirstHeading, setShowFirstHeading] = useState(true);
  const [showGlitch, setShowGlitch] = useState(false);

  useEffect(() => {
    const firstHeadingTimeout = setTimeout(() => setShowFirstHeading(false), 2000);
    const glitchTimeout = setTimeout(() => setShowGlitch(true), 3000);
    return () => {
      clearTimeout(firstHeadingTimeout);
      clearTimeout(glitchTimeout);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-indigo-500 via-blue-500 to-green-500 p-6">
      <style>
        {`
          .intro-heading {
            font-size: 3rem;
            color: #fff;
            text-align: center;
            font-weight: bold;
            text-shadow: 2px 2px #000;
            transition: opacity 1s ease;
          }

          .highlight {
            color: #ffeb3b;
          }

          .mascot-popup {
            animation: popIn 1s ease-out forwards;
          }

          @keyframes popIn {
            0% {
              transform: scale(0.5);
              opacity: 0;
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }
        `}
      </style>

      {/* First Heading */}
      {showFirstHeading && (
        <h1 className="intro-heading mb-8">
          Welcome to the <span className="highlight">World of Data Science!</span>
        </h1>
      )}

      {/* Second Heading */}
      {!showFirstHeading && (
        <h1 className="intro-heading mb-8">
          Let’s Explore the <span className="highlight">World of Data Science!</span>
        </h1>
      )}

      {/* Robot Mascot Section */}
      {showGlitch && (
        <div className="flex flex-col items-center justify-center mb-8 mascot-popup">
          <Player
            autoplay
            loop
            src={robotMascotPath} // Use local JSON file path
            style={{
              width: "150px",
              height: "150px",
            }}
          />
          <div className="mt-4 bg-white bg-opacity-80 text-gray-800 font-bold text-lg p-4 rounded-lg shadow-lg max-w-lg text-center">
            Hello, young data explorers! I’m <span className="highlight">Glitch</span>, your digital guide to the exciting
            world of Data Science. Together, we’ll learn how to collect, analyze, and understand data just like real
            scientists! Ready to begin our journey? Let’s go!
          </div>
        </div>
      )}

      {/* Description */}
      <p className="text-center text-lg text-white max-w-3xl mb-6">
        Data Science is a powerful field that allows us to transform raw data
        into actionable insights. This course will guide you through the
        foundational concepts, tools, and techniques to become a proficient
        data scientist.
      </p>

      {/* Interactive Animation */}
      <div className="w-full max-w-lg mb-6">
        <Player
          autoplay
          loop
          src={dataScienceIntroAnimationUrl}
          style={{
            width: "100%",
            height: "100%",
          }}
        />
      </div>

      {/* Key Objectives */}
      <div className="w-full max-w-4xl bg-white bg-opacity-20 rounded-lg p-6 text-white shadow-lg">
        <h2 className="text-2xl font-bold mb-4 text-center">
          What Will You Learn?
        </h2>
        <ul className="list-disc list-inside text-lg space-y-3">
          <li>Understand what data science is and its applications.</li>
          <li>
            Learn the tools and techniques used to collect, process, and analyze
            data.
          </li>
          <li>Build a strong foundation in data visualization and storytelling.</li>
        </ul>
      </div>

      {/* Encouragement Section */}
      <p className="text-center text-white text-xl font-semibold mt-6">
        Let’s begin your journey into Data Science—step by step, insight by
        insight.
      </p>
    </div>
  );
}
