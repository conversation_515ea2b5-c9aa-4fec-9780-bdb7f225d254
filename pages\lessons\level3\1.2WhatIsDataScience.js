import React from 'react';

export default function WhatIsDataScience() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 text-white p-6 relative">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .image {
                    width: 350px;
                    height: 350px;
                    border-radius: 16px;
                    object-fit: cover;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
                }

                .image-robot-movie {
                    position: absolute;
                    top: 500px; /* Prevents overlap with heading */
                    left: 40px;
                }

                .image-robot-rain {
                    position: absolute;
                    bottom: 40px; /* Adds spacing from bottom */
                    right: 40px;
                }

                .image-robot-scrolling {
                    position: absolute;
                    top: 50%;
                    right: 50px;
                    transform: translateY(-50%);
                }

                .card {
                    max-width: 800px; /* Wider card for better readability */
                    padding: 40px;
                    font-size: 1.8rem; /* Larger font size */
                    background-color: rgba(255, 255, 255, 0.3);
                    border-radius: 16px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
                    margin-top: 80px; /* Adds spacing from heading */
                }

                .heading {
                    font-size: 4rem;
                    font-weight: bold;
                    text-align: center;
                    margin-top: 100px; /* Adds spacing from the top */
                }

                .subheading {
                    font-size: 2rem;
                    text-align: center;
                    max-width: 900px;
                    margin: 20px auto;
                }

                .example-card {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-top: 60px;
                }
                `}
            </style>

            {/* Robot Watching Movie */}
            <img
                src="/level3_robot_watching_movie.jpg"
                alt="Robot Watching Movie"
                className="image image-robot-movie"
            />

            {/* Robot in Rain */}
            <img
                src="/level3_robot_precasting_weather.jpg"
                alt="Robot in Rain"
                className="image image-robot-rain"
            />

            {/* Robot Scrolling Website */}
            <img
                src="/robot_in_rain.png"
                alt="Robot Scrolling Website"
                className="image image-robot-scrolling"
            />

            {/* Heading and Subheading */}
            <h1 className="heading">What is Data Science?</h1>
            <p className="subheading">
                Data Science is like being a <span className="highlight">detective</span>, solving problems by analyzing data to discover patterns, trends, and actionable insights. 
                It combines skills from <span className="highlight">mathematics</span>, <span className="highlight">statistics</span>, <span className="highlight">computer science</span>, 
                and <span className="highlight">domain expertise</span> to extract value from raw information.
            </p>

            {/* Example Section */}
            <div className="card text-black mx-auto">
                <h2 className="text-5xl font-bold mb-6 text-center text-gray-800">
                    Real-Life Examples of Data Science
                </h2>
                <ul className="list-disc list-inside space-y-8 text-gray-700 text-2xl leading-relaxed">
                    <li>
                        <span className="highlight">E-Commerce:</span> Recommending products based on your purchase history 
                        (e.g., Amazon suggesting items &quot;you might like&quot;).
                    </li>
                    <li>
                        <span className="highlight">Healthcare:</span> Using patient data to predict diseases and personalize treatments.
                    </li>
                    <li>
                        <span className="highlight">Social Media:</span> Analyzing user behavior to recommend friends or suggest content 
                        (e.g., Facebook, Instagram, TikTok).
                    </li>
                    <li>
                        <span className="highlight">Transportation:</span> Predicting demand for rides and setting dynamic prices 
                        (e.g., Uber, Lyft).
                    </li>
                    <li>
                        <span className="highlight">Sports Analytics:</span> Analyzing player performance to optimize team strategies.
                    </li>
                </ul>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-2xl font-semibold mt-16 max-w-3xl">
                As a data scientist, you’ll uncover insights that help organizations make smarter decisions. Ready to start exploring the exciting world of data?
            </p>
        </div>
    );
}
