import React from 'react';

export default function WhyVisualizeData() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Why Visualize Data?</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Data visualization transforms raw numbers into <span className="highlight">beautiful charts</span>, making patterns and insights easy to understand and share.
            </p>

            {/* Example 1: Spotting Trends */}
            <div className="content-row">
                <img
                    src="/trends_chart.jpg" /* Adjusted to your actual image path */
                    alt="Spotting Trends"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📈 Spotting Trends</h2>
                    <p className="text-lg">
                        A line chart can show how sales increased over the years or how temperatures changed monthly. Patterns like these are easier to see with visuals!
                    </p>
                </div>
            </div>

            {/* Example 2: Comparing Data */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📊 Comparing Data</h2>
                    <p className="text-lg">
                        Bar charts are great for comparing categories. For instance:
                        <br />
                        - Comparing sales across different regions.<br />
                        - Comparing attendance at events over the years.
                    </p>
                </div>
                <img
                    src="/comparison_chart.jpg" /* Adjusted to your actual image path */
                    alt="Comparing Data"
                    className="image-placeholder"
                />
            </div>

            {/* Example 3: Explaining Insights */}
            <div className="content-row">
                <img
                    src="/insight_pie_chart.jpg" /* Adjusted to your actual image path */
                    alt="Explaining Insights"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🗣️ Explaining Insights</h2>
                    <p className="text-lg">
                        Pie charts and infographics make it easy to explain insights, like how much of a budget is spent on specific tasks or what percentage of users prefer a product.
                    </p>
                </div>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                By visualizing data, you can turn numbers into stories that everyone can understand. Ready to create some amazing charts?
            </p>
        </div>
    );
}
