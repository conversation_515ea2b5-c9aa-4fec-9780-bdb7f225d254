import React from 'react';

export default function CreateYourOwnBarGraph() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }

                .interactive-section {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    margin-top: 20px;
                    text-align: center;
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Create Your Own Bar Graph</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                A <span className="highlight">bar graph</span> is a great way to compare categories and track trends. Let&apos;s explore how to create one step by step!
            </p>

            {/* Example 1: Understanding Bar Graphs */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🔍 What is a Bar Graph?</h2>
                    <p className="text-lg">
                        A bar graph uses vertical or horizontal bars to compare categories. For example:
                        <br />
                        - Showing monthly sales for a store.<br />
                        - Comparing the popularity of different sports.
                    </p>
                </div>
                <img
                    src="/bar_graph_example.jpg" /* Replace with actual image path */
                    alt="Bar Graph Example"
                    className="image-placeholder"
                />
            </div>

            {/* Example 2: Building Your Bar Graph */}
            <div className="content-row">
                <img
                    src="/create_bar_graph.jpg" /* Replace with actual image path */
                    alt="Create Bar Graph Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🛠️ Building Your Bar Graph</h2>
                    <p className="text-lg">
                        Follow these steps:
                        <br />
                        1. Collect your data (e.g., the number of books sold each month).<br />
                        2. Label the categories (e.g., January, February).<br />
                        3. Draw bars for each category proportional to the values.
                    </p>
                </div>
            </div>

            {/* Example 3: Interactive Practice */}
            <div className="interactive-section">
                <h2 className="text-3xl font-bold mb-4">🎨 Practice Creating a Bar Graph</h2>
                <p className="text-lg mb-4">
                    Imagine you&apos;re tracking how many hours you study each day:
                    <br />
                    - Monday: 2 hours<br />
                    - Tuesday: 3 hours<br />
                    - Wednesday: 1.5 hours<br />
                    - Thursday: 4 hours<br />
                    - Friday: 2.5 hours
                </p>
                <p className="text-lg">
                    Use your favorite tools (pen and paper, spreadsheet, or software) to create a bar graph for this data!
                </p>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                Bar graphs make comparisons easy and fun. Start practicing, and see the magic of visualizing your data!
            </p>
        </div>
    );
}
