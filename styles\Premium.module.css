.premiumContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: rgb(var(--background-start-rgb));
  }
  
  .card {
    padding: 2rem;
    border-radius: var(--border-radius);
    background: rgba(var(--card-rgb), 0.1);
    border: 1px solid rgba(var(--card-border-rgb), 0.3);
    max-width: 400px;
    width: 100%;
    text-align: center;
  }
  
  .title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  .description {
    margin-bottom: 1.5rem;
  }
  
  .payButton {
    background-color: #0070f3;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 200ms;
  }
  
  .payButton:hover {
    background-color: #005bb5;
  }
  